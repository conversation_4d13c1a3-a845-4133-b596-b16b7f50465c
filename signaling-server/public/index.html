<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Stream Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #2ed573;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .video-container {
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
            aspect-ratio: 16/9;
        }

        #remoteVideo {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .video-overlay.hidden {
            display: none;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #555;
        }

        .control-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #c82333;
        }

        .info-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }

        .info-item label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 5px;
            display: block;
        }

        .info-item span {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            color: #666;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Video Stream Client</h1>
            <p>Receive video streams from Chrome Extension</p>
        </div>

        <div class="status">
            <div class="status-indicator" id="connectionStatus"></div>
            <span id="statusText">Disconnected</span>
        </div>

        <div class="video-container">
            <video id="remoteVideo" autoplay playsinline controls>
                Your browser does not support video playback.
            </video>
            <div class="video-overlay" id="videoOverlay">
                <h3>No Video Stream</h3>
                <p>Connect to the signaling server and start streaming from the Chrome extension</p>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="serverUrl">Signaling Server URL:</label>
                <input type="text" id="serverUrl" value="ws://localhost:8080" placeholder="ws://localhost:8080">
            </div>
            <button id="connectBtn" class="btn btn-primary">Connect</button>
            <button id="disconnectBtn" class="btn btn-danger" disabled>Disconnect</button>
        </div>

        <div class="info-panel">
            <h3>Connection Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <label>Client ID</label>
                    <span id="clientId">-</span>
                </div>
                <div class="info-item">
                    <label>Connection State</label>
                    <span id="connectionState">disconnected</span>
                </div>
                <div class="info-item">
                    <label>ICE Connection State</label>
                    <span id="iceConnectionState">-</span>
                </div>
                <div class="info-item">
                    <label>Video Resolution</label>
                    <span id="videoResolution">-</span>
                </div>
            </div>

            <h4>Activity Log</h4>
            <div class="log-container" id="logContainer">
                <div class="log-entry">Client initialized</div>
            </div>
        </div>
    </div>

    <script src="client.js"></script>
</body>
</html>

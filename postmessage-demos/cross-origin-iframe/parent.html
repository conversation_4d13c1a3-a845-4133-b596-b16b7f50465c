<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cross-Origin Iframe Communication - Parent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .iframe-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex: 1;
            min-width: 200px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Cross-Origin Iframe Communication - Parent Page</h1>
        <p><strong>Origin:</strong> <span id="parentOrigin"></span></p>
        <p>This page demonstrates secure cross-origin communication between a parent page and an iframe from a different origin using the postMessage API.</p>
        
        <div id="connectionStatus" class="status disconnected">
            ❌ Iframe not loaded yet
        </div>

        <div class="controls">
            <input type="text" id="messageInput" placeholder="Enter message to send to iframe..." value="Hello from parent!">
            <button class="btn-primary" onclick="sendMessage()">Send Message</button>
            <button class="btn-success" onclick="sendData()">Send Data Object</button>
            <button class="btn-warning" onclick="requestInfo()">Request Iframe Info</button>
        </div>

        <div class="iframe-container">
            <iframe id="childFrame" src="http://localhost:3002/cross-origin-iframe/iframe.html"></iframe>
        </div>

        <h3>📋 Message Log</h3>
        <div id="messageLog" class="log"></div>
        
        <button onclick="clearLog()" style="margin-top: 10px; padding: 5px 10px;">Clear Log</button>
    </div>

    <script>
        // Security configuration
        const ALLOWED_ORIGINS = ['http://localhost:3002'];
        const IFRAME_ORIGIN = 'http://localhost:3002';
        
        // DOM elements
        const iframe = document.getElementById('childFrame');
        const messageInput = document.getElementById('messageInput');
        const messageLog = document.getElementById('messageLog');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // State
        let iframeLoaded = false;
        
        // Initialize
        document.getElementById('parentOrigin').textContent = window.location.origin;
        
        // Message counter for tracking
        let messageCounter = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            
            // Also log to console for debugging
            console.log(`Parent: ${message}`);
        }
        
        function updateConnectionStatus(connected) {
            iframeLoaded = connected;
            if (connected) {
                connectionStatus.className = 'status connected';
                connectionStatus.textContent = '✅ Iframe loaded and ready for communication';
            } else {
                connectionStatus.className = 'status disconnected';
                connectionStatus.textContent = '❌ Iframe not loaded yet';
            }
        }
        
        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            log(`Received message from: ${event.origin}`, 'receive');
            
            // Security check: validate origin
            if (!ALLOWED_ORIGINS.includes(event.origin)) {
                log(`❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`, 'security');
                return;
            }
            
            // Validate message structure
            if (!event.data || typeof event.data !== 'object') {
                log(`❌ SECURITY: Invalid message format received`, 'security');
                return;
            }
            
            const { type, payload, messageId } = event.data;
            
            log(`Message type: ${type}, ID: ${messageId}`, 'receive');
            log(`Payload: ${JSON.stringify(payload)}`, 'receive');
            
            // Handle different message types
            switch (type) {
                case 'iframe_ready':
                    updateConnectionStatus(true);
                    log('✅ Iframe is ready for communication', 'success');
                    break;
                    
                case 'iframe_info':
                    log(`📊 Iframe info received:`, 'info');
                    log(`  - URL: ${payload.url}`, 'info');
                    log(`  - User Agent: ${payload.userAgent}`, 'info');
                    log(`  - Viewport: ${payload.viewport.width}x${payload.viewport.height}`, 'info');
                    break;
                    
                case 'echo_response':
                    log(`🔄 Echo response: "${payload.message}"`, 'success');
                    break;
                    
                case 'error':
                    log(`❌ Error from iframe: ${payload.message}`, 'error');
                    break;
                    
                default:
                    log(`⚠️ Unknown message type: ${type}`, 'warning');
            }
        });
        
        function sendMessage() {
            if (!iframeLoaded) {
                log('❌ Cannot send message: iframe not loaded', 'error');
                return;
            }
            
            const message = messageInput.value.trim();
            if (!message) {
                log('❌ Cannot send empty message', 'error');
                return;
            }
            
            const messageData = {
                type: 'echo_request',
                payload: { message: message },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                iframe.contentWindow.postMessage(messageData, IFRAME_ORIGIN);
                log(`📤 Sent echo request: "${message}" (ID: ${messageCounter})`, 'send');
            } catch (error) {
                log(`❌ Failed to send message: ${error.message}`, 'error');
            }
        }
        
        function sendData() {
            if (!iframeLoaded) {
                log('❌ Cannot send data: iframe not loaded', 'error');
                return;
            }
            
            const complexData = {
                type: 'data_update',
                payload: {
                    user: {
                        id: 123,
                        name: 'John Doe',
                        preferences: {
                            theme: 'dark',
                            language: 'en'
                        }
                    },
                    timestamp: new Date().toISOString(),
                    metadata: {
                        source: 'parent_window',
                        version: '1.0.0'
                    }
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                iframe.contentWindow.postMessage(complexData, IFRAME_ORIGIN);
                log(`📤 Sent complex data object (ID: ${messageCounter})`, 'send');
                log(`Data: ${JSON.stringify(complexData.payload, null, 2)}`, 'send');
            } catch (error) {
                log(`❌ Failed to send data: ${error.message}`, 'error');
            }
        }
        
        function requestInfo() {
            if (!iframeLoaded) {
                log('❌ Cannot request info: iframe not loaded', 'error');
                return;
            }
            
            const requestData = {
                type: 'info_request',
                payload: {},
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                iframe.contentWindow.postMessage(requestData, IFRAME_ORIGIN);
                log(`📤 Requested iframe information (ID: ${messageCounter})`, 'send');
            } catch (error) {
                log(`❌ Failed to request info: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Handle iframe load
        iframe.addEventListener('load', function() {
            log('🔄 Iframe loaded, waiting for ready signal...', 'info');
        });
        
        // Handle iframe load errors
        iframe.addEventListener('error', function() {
            log('❌ Failed to load iframe', 'error');
            updateConnectionStatus(false);
        });
        
        // Handle Enter key in message input
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initial log entry
        log('🚀 Parent page initialized, loading iframe...', 'info');
        log(`Parent origin: ${window.location.origin}`, 'info');
        log(`Iframe origin: ${IFRAME_ORIGIN}`, 'info');
    </script>
</body>
</html>

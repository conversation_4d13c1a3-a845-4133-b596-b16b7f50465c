# Chrome Extension Video Streaming with CDP

A Chrome extension that captures video content from browser tabs and streams it to external web clients using WebRTC technology, enhanced with Chrome DevTools Protocol (CDP) integration.

## Features

- **Tab Video Capture**: Capture video content from any browser tab
- **WebRTC Streaming**: Stream video to external web clients with low latency
- **CDP Integration**: Enhanced browser control and access beyond standard APIs
- **Real-time Communication**: WebSocket signaling server for connection establishment
- **Dual Display**: Local preview in extension popup + remote streaming
- **Performance Monitoring**: Real-time statistics and connection monitoring

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Chrome Tab     │    │  Extension       │    │  Web Client     │
│                 │    │                  │    │                 │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │  ┌───────────┐  │
│  │  Content  │  │◄───┤  │ Background  │ │    │  │  Video    │  │
│  │  Script   │  │    │  │  Script     │ │    │  │  Player   │  │
│  └───────────┘  │    │  └─────────────┘ │    │  └───────────┘  │
│                 │    │         │        │    │         ▲       │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │         │       │
│  │    CDP    │  │◄───┤  │   Popup     │ │    │         │       │
│  │  Client   │  │    │  │     UI      │ │    │         │       │
│  └───────────┘  │    │  └─────────────┘ │    │         │       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                         │
                                └─────────────────────────┘
                                    WebRTC + Signaling
```

## Quick Start

### 1. Install Dependencies

```bash
# Install signaling server dependencies
cd signaling-server
npm install
```

### 2. Start Signaling Server

```bash
# Start the WebSocket signaling server
cd signaling-server
npm start
```

The server will start on `http://localhost:8080`

### 3. Load Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked" and select the project root directory
4. The extension should appear in your extensions list

### 4. Test the Setup

1. **Open Web Client**: Navigate to `http://localhost:8080` in a new tab
2. **Open Extension**: Click the extension icon in Chrome toolbar
3. **Start Capture**: Click "Start Capture" in the extension popup
4. **Connect to Server**: Click "Connect" in both extension popup and web client
5. **Start Streaming**: Click "Start Streaming" in the extension popup
6. **View Stream**: Video should appear in the web client

## Detailed Setup

### Prerequisites

- **Node.js** (v14 or higher)
- **Chrome Browser** (latest version recommended)
- **Network Access** (for STUN servers and local WebSocket connections)

### Extension Installation

1. **Download/Clone** the project files
2. **Install Icons** (optional): Add icon files to the `icons/` directory
3. **Load Extension**:
   - Open `chrome://extensions/`
   - Enable Developer mode
   - Click "Load unpacked"
   - Select the project root directory

### Signaling Server Setup

```bash
# Navigate to signaling server directory
cd signaling-server

# Install dependencies
npm install

# Start server (development mode with auto-restart)
npm run dev

# Or start in production mode
npm start
```

### Configuration

#### Extension Configuration

- **Signaling Server URL**: Default is `ws://localhost:8080`
- **Video Quality**: Configurable in video-capture.js
- **WebRTC Settings**: Configurable in webrtc-manager.js

#### Server Configuration

- **Port**: Set via `PORT` environment variable (default: 8080)
- **CORS**: Enabled for all origins (modify in server.js for production)

## Usage Guide

### Starting a Stream

1. **Open Target Tab**: Navigate to the content you want to stream
2. **Open Extension Popup**: Click the extension icon
3. **Start Capture**:
   - Click "Start Capture"
   - Grant screen capture permissions when prompted
   - Select the tab you want to capture
4. **Connect to Signaling Server**:
   - Ensure server URL is correct
   - Click "Connect"
   - Wait for "Connected" status
5. **Start Streaming**:
   - Click "Start Streaming"
   - Extension will begin broadcasting to connected clients

### Viewing a Stream

1. **Open Web Client**: Navigate to `http://localhost:8080`
2. **Connect to Server**: Click "Connect" button
3. **Wait for Stream**: Video will appear automatically when extension starts streaming

### Monitoring

- **Extension Popup**: Shows capture status, connection info, and streaming statistics
- **Web Client**: Displays connection state, video resolution, and activity log
- **Server Logs**: Console output shows client connections and message routing

## API Reference

### Extension Messages

The extension background script handles these message types:

```javascript
// Start video capture
chrome.runtime.sendMessage({
  action: "startCapture",
  tabId: tabId,
});

// Connect to signaling server
chrome.runtime.sendMessage({
  action: "connectSignaling",
  serverUrl: "ws://localhost:8080",
});

// Start streaming
chrome.runtime.sendMessage({
  action: "startStreaming",
  tabId: tabId,
});
```

### CDP Integration

Enhanced capabilities through Chrome DevTools Protocol:

```javascript
// Attach CDP to tab
chrome.runtime.sendMessage({
  action: "attachCDP",
  tabId: tabId,
});

// Get enhanced page information
chrome.runtime.sendMessage({
  action: "getPageInfo",
  tabId: tabId,
});

// Inject capture helper
chrome.runtime.sendMessage({
  action: "injectCaptureHelper",
  tabId: tabId,
});
```

### WebRTC Signaling

Signaling messages between extension and web clients:

```javascript
// Offer (Extension → Client)
{
  type: 'offer',
  clientId: 'client_123',
  data: RTCSessionDescription
}

// Answer (Client → Extension)
{
  type: 'answer',
  data: RTCSessionDescription
}

// ICE Candidate
{
  type: 'ice-candidate',
  data: RTCIceCandidate
}
```

## Troubleshooting

### Common Issues

1. **"User cancelled capture"**

   - Ensure you select a tab in the capture dialog
   - Grant necessary permissions when prompted

2. **"Extension not connected"**

   - Check signaling server is running
   - Verify WebSocket URL is correct
   - Check browser console for connection errors

3. **No video in web client**

   - Ensure both extension and client are connected to signaling server
   - Check WebRTC connection state in client logs
   - Verify firewall/network settings allow WebRTC traffic

4. **Poor video quality**
   - Adjust capture constraints in video-capture.js
   - Check network bandwidth
   - Monitor CPU usage during capture

### Debug Mode

Enable detailed logging:

1. **Extension**: Open Chrome DevTools on extension popup
2. **Background Script**: Go to `chrome://extensions/` → Extension details → "Inspect views: background page"
3. **Web Client**: Open browser DevTools on client page
4. **Signaling Server**: Check console output

### Performance Optimization

- **Reduce Resolution**: Lower video capture resolution for better performance
- **Adjust Frame Rate**: Reduce frame rate if experiencing lag
- **Network Optimization**: Use local network for testing, consider TURN servers for production
- **Browser Resources**: Close unnecessary tabs to free up system resources

## Security Considerations

### Permissions

The extension requires these permissions:

- `activeTab`: Access current tab content
- `tabs`: Tab management
- `desktopCapture`: Screen/tab capture
- `debugger`: CDP access
- `storage`: Settings storage

### Network Security

- **Local Development**: Default setup uses localhost connections
- **Production Deployment**:
  - Use HTTPS for signaling server
  - Implement authentication/authorization
  - Configure proper CORS policies
  - Use secure WebSocket connections (WSS)

### Privacy

- **Content Capture**: Extension can capture any tab content
- **User Consent**: Always request explicit permission before capturing
- **Data Handling**: No video data is stored; streaming is real-time only

## Development

### Project Structure

```
├── manifest.json              # Extension manifest
├── background.js             # Service worker
├── content.js               # Content script
├── injected.js             # Page injection script
├── popup.html/css/js       # Extension popup
├── cdp-client.js          # CDP integration
├── video-capture.js       # Video capture manager
├── webrtc-manager.js     # WebRTC streaming
├── signaling-server/     # WebSocket server
│   ├── server.js        # Main server file
│   ├── package.json     # Dependencies
│   └── public/         # Web client files
└── icons/              # Extension icons
```

### Adding Features

1. **New Capture Sources**: Extend video-capture.js
2. **Enhanced CDP**: Add methods to cdp-client.js
3. **UI Improvements**: Modify popup.html/css/js
4. **Server Features**: Extend signaling-server/server.js

### Testing

```bash
# Test signaling server
cd signaling-server
npm test  # (if tests are added)

# Manual testing checklist:
# 1. Extension loads without errors
# 2. Can capture tab video
# 3. Signaling server connects
# 4. WebRTC connection establishes
# 5. Video streams to client
# 6. Cleanup works properly
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Limitations and Constraints

### Chrome Extension API Restrictions

- **Desktop Capture Permission**: Requires user interaction for each capture session
- **Manifest V3**: Service worker limitations (no persistent background scripts)
- **Cross-Origin**: Limited access to cross-origin content
- **Tab Permissions**: Requires activeTab or tabs permission

### CDP Access Limitations

- **Security Boundaries**: Cannot access all browser internals
- **Debugger API**: Limited to what chrome.debugger exposes
- **Performance Impact**: CDP operations can affect browser performance
- **Version Compatibility**: CDP features may vary across Chrome versions

### Video Quality Constraints

- **Resolution Limits**: Browser-imposed maximum resolution (typically 1920x1080)
- **Frame Rate**: Usually capped at 30fps for tab capture
- **Compression**: WebRTC encoding introduces compression artifacts
- **Bandwidth**: Quality depends on available network bandwidth

### Network and Performance

- **WebRTC Limitations**: Requires STUN/TURN servers for NAT traversal
- **Latency**: Network conditions affect streaming latency
- **CPU Usage**: Video encoding/decoding is CPU intensive
- **Memory Usage**: Multiple streams can consume significant memory

## Capabilities and Advantages

### Enhanced Access through CDP

- **Runtime Evaluation**: Execute JavaScript in page context
- **DOM Manipulation**: Enhanced DOM access and modification
- **Network Monitoring**: Track network requests and responses
- **Performance Metrics**: Access to detailed performance data
- **Debugging Features**: Advanced debugging capabilities

### WebRTC Benefits

- **Peer-to-Peer**: Direct connection between extension and clients
- **Low Latency**: Minimal delay for real-time streaming
- **Adaptive Bitrate**: Automatic quality adjustment based on network
- **NAT Traversal**: Built-in support for firewall/NAT traversal
- **Cross-Platform**: Works across different operating systems

### Browser Integration Advantages

- **Native Performance**: Leverages browser's native video processing
- **Security Context**: Operates within browser's security model
- **No Installation**: No additional software required for clients
- **Automatic Updates**: Extension updates through Chrome Web Store

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review browser console logs
3. Check signaling server logs
4. Open an issue with detailed information

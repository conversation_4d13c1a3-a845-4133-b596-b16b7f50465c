# Chrome DevTools Protocol (CDP) Monitoring

This directory contains advanced debugging and monitoring tools for postMessage communications using Chrome DevTools Protocol (CDP) and Puppeteer.

## 🎯 What This Provides

- **Real-time monitoring** of all postMessage events across browser contexts
- **Visual debugging interface** with live message logs and statistics
- **Cross-origin communication inspection** with origin validation tracking
- **Performance metrics** including message latency and throughput
- **Automated demo launching** for quick testing scenarios
- **Message flow visualization** and network topology mapping

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Install WebSocket support for real-time communication
npm install ws
```

### 2. Start the Monitor
```bash
# Start the CDP monitor (includes debug UI)
npm run cdp-monitor
```

### 3. Access Debug Interface
- **Debug UI**: http://localhost:3003
- **WebSocket API**: ws://localhost:3004

## 🔧 Features

### Real-Time Message Monitoring
```javascript
// Automatically captures all postMessage calls
window.postMessage = function(message, targetOrigin, transfer) {
    console.log('📤 PostMessage SEND:', {
        message,
        targetOrigin,
        origin: window.location.origin,
        timestamp: Date.now()
    });
    return originalPostMessage.call(this, message, targetOrigin, transfer);
};
```

### Cross-Origin Security Tracking
- **Origin validation monitoring** - Track which origins are communicating
- **Security violation detection** - Alert on unauthorized cross-origin attempts
- **Message structure validation** - Verify message format compliance
- **CORS policy enforcement** - Monitor CORS header compliance

### Performance Analytics
- **Message latency measurement** - Track round-trip times for ping-pong patterns
- **Throughput analysis** - Messages per second across different contexts
- **Error rate monitoring** - Track failed message deliveries
- **Memory usage tracking** - Monitor resource consumption

### Debug Interface Features

#### Live Statistics Dashboard
```
📊 Total Messages: 1,247
🌐 Active Pages: 5
❌ Errors: 3
⚡ Avg Latency: 12ms
```

#### Message Flow Visualization
```
Tab 1 ──────────► Tab 2
  │                 │
  └─► Child 1       └─► Child 2
      Child 3           Child 4
```

#### Filtering and Search
- Filter by message type
- Search by origin or content
- Time-based filtering
- Error-only view

## 🛠️ Advanced Usage

### Custom Message Tracking
```javascript
// Add custom tracking for specific message types
monitor.addMessageFilter({
    type: 'emergency_signal',
    handler: (event) => {
        console.log('🚨 Emergency detected:', event);
        // Custom handling for emergency messages
    }
});
```

### Performance Profiling
```javascript
// Track specific communication patterns
monitor.profileCommunication({
    pattern: 'tab1_to_children',
    threshold: 100, // Alert if latency > 100ms
    callback: (metrics) => {
        console.log('Performance metrics:', metrics);
    }
});
```

### Security Auditing
```javascript
// Monitor for security violations
monitor.enableSecurityAudit({
    allowedOrigins: ['http://localhost:3001', 'http://localhost:3002'],
    strictMode: true,
    onViolation: (violation) => {
        console.warn('Security violation:', violation);
    }
});
```

## 📡 WebSocket API

### Connect to Monitor
```javascript
const ws = new WebSocket('ws://localhost:3004');

ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    handleMonitorEvent(data);
};
```

### Message Types

#### Monitoring Events
```javascript
// Real-time postMessage event
{
    type: 'postmessage_event',
    data: {
        direction: 'send|receive',
        origin: 'http://localhost:3001',
        targetOrigin: 'http://localhost:3002',
        messageType: 'ping_request',
        timestamp: 1640995200000,
        latency: 15 // ms (for responses)
    }
}
```

#### Control Commands
```javascript
// Start monitoring
ws.send(JSON.stringify({
    type: 'start_monitoring'
}));

// Open demo page
ws.send(JSON.stringify({
    type: 'open_demo',
    demo: 'cross-origin-iframe'
}));
```

## 🔍 Debugging Scenarios

### Cross-Origin Issues
```javascript
// Monitor CORS violations
monitor.on('cors_violation', (event) => {
    console.error('CORS violation detected:', {
        origin: event.origin,
        targetOrigin: event.targetOrigin,
        blocked: true,
        reason: 'Origin not allowed'
    });
});
```

### Message Delivery Failures
```javascript
// Track failed message deliveries
monitor.on('delivery_failure', (event) => {
    console.error('Message delivery failed:', {
        messageId: event.messageId,
        reason: event.reason,
        retryCount: event.retryCount
    });
});
```

### Performance Bottlenecks
```javascript
// Identify slow communication paths
monitor.on('slow_communication', (event) => {
    console.warn('Slow communication detected:', {
        path: event.path,
        latency: event.latency,
        threshold: event.threshold
    });
});
```

## 🎮 Demo Integration

### Automated Demo Testing
```javascript
// Test all demos automatically
async function runDemoTests() {
    const demos = [
        'cross-origin-iframe',
        'inter-tab',
        'parent-child-window',
        'multi-tab-complex'
    ];
    
    for (const demo of demos) {
        await monitor.openDemo(demo);
        await monitor.runTestScenario(demo);
        await monitor.collectMetrics(demo);
    }
}
```

### Scenario Recording
```javascript
// Record user interactions for replay
monitor.startRecording({
    scenario: 'complex_multi_tab_test',
    duration: 60000, // 1 minute
    includeUserActions: true
});
```

## 📊 Metrics and Analytics

### Communication Patterns
- **Hub-and-spoke** - Central tab communicating with multiple children
- **Mesh network** - All tabs communicating with each other
- **Chain relay** - Messages passed through intermediate nodes
- **Broadcast** - One-to-many communication patterns

### Performance Metrics
```javascript
{
    totalMessages: 1247,
    messagesPerSecond: 12.3,
    averageLatency: 15.2,
    errorRate: 0.24,
    peakLatency: 156,
    communicationPaths: [
        { from: 'tab1', to: 'tab2', count: 423, avgLatency: 12 },
        { from: 'tab2', to: 'child1', count: 234, avgLatency: 8 }
    ]
}
```

### Error Analysis
```javascript
{
    corsViolations: 3,
    deliveryFailures: 1,
    timeouts: 2,
    malformedMessages: 0,
    securityViolations: 1
}
```

## 🔧 Configuration

### Monitor Settings
```javascript
const config = {
    // Monitoring options
    captureAllMessages: true,
    includeInternalMessages: false,
    trackPerformance: true,
    
    // Security settings
    strictOriginValidation: true,
    allowedOrigins: ['http://localhost:3001', 'http://localhost:3002'],
    
    // Performance settings
    latencyThreshold: 100, // ms
    messageRateLimit: 1000, // per second
    
    // Debug options
    verboseLogging: true,
    saveMessageHistory: true,
    maxHistorySize: 10000
};
```

### Custom Handlers
```javascript
// Add custom message handlers
monitor.addHandler('emergency_signal', (event) => {
    // Custom emergency handling
    notifyAdministrator(event);
    logSecurityEvent(event);
});

monitor.addHandler('performance_degradation', (event) => {
    // Performance issue handling
    adjustMessageRate(event.newRate);
    alertDevelopers(event);
});
```

## 🚀 Real-World Applications

### Production Monitoring
- Monitor postMessage usage in production applications
- Track communication patterns and performance
- Detect security violations and unauthorized access
- Generate compliance reports

### Development Debugging
- Debug complex multi-window applications
- Visualize message flow between components
- Identify performance bottlenecks
- Test cross-origin communication scenarios

### Security Auditing
- Monitor for unauthorized cross-origin communication
- Track message content for sensitive data leaks
- Validate origin restrictions and CORS policies
- Generate security compliance reports

### Performance Optimization
- Identify slow communication paths
- Optimize message routing strategies
- Reduce unnecessary message overhead
- Implement efficient batching strategies

This CDP monitoring system provides enterprise-level debugging and monitoring capabilities for complex postMessage-based applications.

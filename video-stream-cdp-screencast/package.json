{"name": "video-stream-cdp-screencast", "version": "1.0.0", "description": "POC system for streaming browser content using Chrome DevTools Protocol (CDP) screencast functionality", "main": "index.js", "scripts": {"start": "node scripts/start.js", "dev": "concurrently \"npm run start:server\" \"npm run start:client\"", "start:server": "cd signaling-server && npm start", "start:client": "cd client-ui && npm start", "setup": "node scripts/setup.js", "test": "node scripts/test.js", "clean": "node scripts/clean.js"}, "keywords": ["chrome-devtools-protocol", "cdp", "screencast", "webrtc", "video-streaming", "browser-automation", "puppeteer"], "author": "CDP Screencast POC", "license": "MIT", "dependencies": {"concurrently": "^8.2.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "."}}
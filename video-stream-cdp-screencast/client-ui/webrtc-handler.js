/**
 * WebRTC Handler for Client UI
 *
 * Manages WebRTC peer connections for receiving screencast streams
 * from the CDP manager tab.
 */

class WebRTCHandler {
  constructor() {
    this.debug = true;
    this.peerConnection = null;
    this.dataChannel = null;
    this.signalingSocket = null;
    this.clientId = null;

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: "stun:stun.cloudflare.com:3478" },
        { urls: "stun:stun.l.google.com:19302" },
      ],
      iceCandidatePoolSize: 10,
    };

    // Stream handling
    this.currentStream = null;
    this.frameCallback = null;
    this.statsCallback = null;

    // Statistics
    this.stats = {
      framesReceived: 0,
      bytesReceived: 0,
      lastFrameTime: 0,
      frameRate: 0,
      dataRate: 0,
    };

    // Connection state
    this.connectionState = "disconnected";
    this.lastActivity = 0;

    this.setupStatsMonitoring();
  }

  setSignalingSocket(socket) {
    this.signalingSocket = socket;
  }

  setFrameCallback(callback) {
    this.frameCallback = callback;
  }

  setStatsCallback(callback) {
    this.statsCallback = callback;
  }

  async initializePeerConnection() {
    this.log("Initializing WebRTC peer connection...");

    try {
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Set up event handlers
      this.setupPeerConnectionHandlers();

      this.log("WebRTC peer connection initialized");
      return true;
    } catch (error) {
      this.error("Failed to initialize peer connection:", error);
      throw error;
    }
  }

  setupPeerConnectionHandlers() {
    // Connection state monitoring
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection.connectionState;
      this.log(`WebRTC connection state: ${state}`);
      this.connectionState = state;

      if (this.statsCallback) {
        this.statsCallback({ connectionState: state });
      }
    };

    // ICE connection state
    this.peerConnection.oniceconnectionstatechange = () => {
      this.log(
        `ICE connection state: ${this.peerConnection.iceConnectionState}`
      );
    };

    // ICE candidate handling
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.signalingSocket) {
        this.sendSignalingMessage({
          type: "webrtc-ice-candidate",
          candidate: event.candidate,
          fromClientId: this.clientId,
        });
      }
    };

    // Data channel handling
    this.peerConnection.ondatachannel = (event) => {
      this.log("Data channel received");
      this.dataChannel = event.channel;
      this.setupDataChannelHandlers();
    };

    // Track handling (for future video track support)
    this.peerConnection.ontrack = (event) => {
      this.log("Track received:", event.track.kind);
      if (event.track.kind === "video") {
        this.handleVideoTrack(event.streams[0]);
      }
    };
  }

  setupDataChannelHandlers() {
    this.dataChannel.onopen = () => {
      this.log("Data channel opened");
      if (this.statsCallback) {
        this.statsCallback({ dataChannelOpen: true });
      }
    };

    this.dataChannel.onclose = () => {
      this.log("Data channel closed");
      if (this.statsCallback) {
        this.statsCallback({ dataChannelOpen: false });
      }
    };

    this.dataChannel.onerror = (error) => {
      this.error("Data channel error:", error);
    };

    this.dataChannel.onmessage = (event) => {
      this.handleDataChannelMessage(event.data);
    };
  }

  handleDataChannelMessage(data) {
    try {
      const message = JSON.parse(data);
      this.lastActivity = Date.now();

      switch (message.type) {
        case "screencastFrame":
          this.handleScreencastFrame(message);
          break;
        case "pong":
          this.handlePong(message);
          break;
        default:
          this.log("Unknown data channel message:", message.type);
      }
    } catch (error) {
      this.error("Error parsing data channel message:", error);
    }
  }

  handleScreencastFrame(frameData) {
    this.stats.framesReceived++;
    this.stats.bytesReceived += frameData.data ? frameData.data.length : 0;

    const now = Date.now();

    // Calculate frame rate
    if (this.stats.lastFrameTime > 0) {
      const timeDiff = now - this.stats.lastFrameTime;
      this.stats.frameRate = 1000 / timeDiff;
    }
    this.stats.lastFrameTime = now;

    // Call frame callback
    if (this.frameCallback) {
      this.frameCallback(frameData);
    }

    // Update statistics
    if (this.statsCallback) {
      this.statsCallback({
        framesReceived: this.stats.framesReceived,
        frameRate: this.stats.frameRate.toFixed(1),
        lastFrameTime: now,
      });
    }
  }

  handleVideoTrack(stream) {
    this.log("Handling video track");
    this.currentStream = stream;

    if (this.frameCallback) {
      this.frameCallback({ type: "videoTrack", stream: stream });
    }
  }

  async handleOffer(offer) {
    this.log("Handling WebRTC offer");

    try {
      if (!this.peerConnection) {
        await this.initializePeerConnection();
      }

      await this.peerConnection.setRemoteDescription(
        new RTCSessionDescription(offer)
      );

      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      this.sendSignalingMessage({
        type: "webrtc-answer",
        answer: answer,
        fromClientId: this.clientId,
      });

      this.log("WebRTC answer sent");
    } catch (error) {
      this.error("Failed to handle offer:", error);
      throw error;
    }
  }

  async handleAnswer(answer) {
    this.log("Handling WebRTC answer");

    try {
      await this.peerConnection.setRemoteDescription(
        new RTCSessionDescription(answer)
      );
      this.log("Remote description set");
    } catch (error) {
      this.error("Failed to handle answer:", error);
      throw error;
    }
  }

  async handleIceCandidate(candidate) {
    try {
      if (this.peerConnection) {
        await this.peerConnection.addIceCandidate(
          new RTCIceCandidate(candidate)
        );
      }
    } catch (error) {
      this.error("Failed to add ICE candidate:", error);
    }
  }

  sendSignalingMessage(message) {
    if (
      this.signalingSocket &&
      this.signalingSocket.readyState === WebSocket.OPEN
    ) {
      this.signalingSocket.send(JSON.stringify(message));
    }
  }

  sendDataChannelMessage(message) {
    if (this.dataChannel && this.dataChannel.readyState === "open") {
      try {
        this.dataChannel.send(JSON.stringify(message));
      } catch (error) {
        this.error("Failed to send data channel message:", error);
      }
    }
  }

  // Quality control methods
  requestQualityChange(quality) {
    this.sendDataChannelMessage({
      type: "qualityRequest",
      quality: quality,
    });
  }

  requestResolutionChange(width, height) {
    this.sendDataChannelMessage({
      type: "qualityRequest",
      resolution: { width, height },
    });
  }

  // Ping/pong for connection monitoring
  sendPing() {
    this.sendDataChannelMessage({
      type: "ping",
      timestamp: Date.now(),
    });
  }

  handlePong(message) {
    const latency = Date.now() - message.timestamp;
    if (this.statsCallback) {
      this.statsCallback({ latency: latency });
    }
  }

  // Statistics monitoring
  setupStatsMonitoring() {
    setInterval(() => {
      this.updateDataRate();
      if (this.dataChannel && this.dataChannel.readyState === "open") {
        this.sendPing();
      }
    }, 5000);
  }

  updateDataRate() {
    const now = Date.now();
    const timeDiff = now - (this.lastStatsUpdate || now);
    const bytesDiff = this.stats.bytesReceived - (this.lastBytesReceived || 0);

    if (timeDiff > 0) {
      this.stats.dataRate = (bytesDiff / timeDiff) * 1000; // bytes per second
    }

    this.lastStatsUpdate = now;
    this.lastBytesReceived = this.stats.bytesReceived;

    if (this.statsCallback) {
      this.statsCallback({
        dataRate: (this.stats.dataRate / 1024).toFixed(1), // KB/s
      });
    }
  }

  getConnectionStats() {
    return {
      connectionState: this.connectionState,
      dataChannelOpen: this.dataChannel?.readyState === "open",
      framesReceived: this.stats.framesReceived,
      frameRate: this.stats.frameRate,
      dataRate: this.stats.dataRate,
      bytesReceived: this.stats.bytesReceived,
      lastActivity: this.lastActivity,
    };
  }

  // Cleanup methods
  cleanup() {
    this.log("Cleaning up WebRTC connection");

    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.currentStream) {
      this.currentStream.getTracks().forEach((track) => track.stop());
      this.currentStream = null;
    }

    this.connectionState = "disconnected";
    this.stats = {
      framesReceived: 0,
      bytesReceived: 0,
      lastFrameTime: 0,
      frameRate: 0,
      dataRate: 0,
    };
  }

  log(...args) {
    if (this.debug) {
      console.log("[WebRTCHandler]", ...args);
    }
  }

  error(...args) {
    console.error("[WebRTCHandler ERROR]", ...args);
  }
}

// Export for use in other modules
if (typeof window !== "undefined") {
  window.WebRTCHandler = WebRTCHandler;
}

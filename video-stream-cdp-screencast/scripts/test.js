#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🧪 CDP Screencast POC Test Suite\n');

class TestRunner {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.tests = [];
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  addTest(name, testFn) {
    this.tests.push({ name, testFn });
  }

  async runTests() {
    console.log(`Running ${this.tests.length} tests...\n`);
    
    for (const test of this.tests) {
      this.results.total++;
      
      try {
        console.log(`🔍 ${test.name}...`);
        await test.testFn();
        console.log(`✅ ${test.name} - PASSED\n`);
        this.results.passed++;
      } catch (error) {
        console.log(`❌ ${test.name} - FAILED`);
        console.log(`   Error: ${error.message}\n`);
        this.results.failed++;
      }
    }
    
    this.printResults();
  }

  printResults() {
    console.log('📊 Test Results:');
    console.log(`   Total: ${this.results.total}`);
    console.log(`   Passed: ${this.results.passed}`);
    console.log(`   Failed: ${this.results.failed}`);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 All tests passed!');
    } else {
      console.log('\n⚠️  Some tests failed. Check the output above for details.');
    }
  }

  async checkFileExists(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
  }

  async checkDirectoryExists(dirPath) {
    const fullPath = path.join(this.projectRoot, dirPath);
    if (!fs.existsSync(fullPath) || !fs.statSync(fullPath).isDirectory()) {
      throw new Error(`Directory not found: ${dirPath}`);
    }
  }

  async checkPackageJson(componentPath) {
    const packagePath = path.join(this.projectRoot, componentPath, 'package.json');
    if (!fs.existsSync(packagePath)) {
      throw new Error(`package.json not found in ${componentPath}`);
    }
    
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    if (!packageData.name || !packageData.version) {
      throw new Error(`Invalid package.json in ${componentPath}`);
    }
  }

  async testPortAvailable(port) {
    return new Promise((resolve) => {
      const net = require('net');
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => resolve(true));
        server.close();
      });
      
      server.on('error', () => resolve(false));
    });
  }

  async runCommand(command, cwd = this.projectRoot) {
    return new Promise((resolve, reject) => {
      const [cmd, ...args] = command.split(' ');
      const process = spawn(cmd, args, { cwd, stdio: 'pipe' });
      
      let stdout = '';
      let stderr = '';
      
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });
      
      // Timeout after 30 seconds
      setTimeout(() => {
        process.kill();
        reject(new Error('Command timeout'));
      }, 30000);
    });
  }
}

// Initialize test runner
const runner = new TestRunner();

// Project Structure Tests
runner.addTest('Project structure exists', async () => {
  await runner.checkDirectoryExists('signaling-server');
  await runner.checkDirectoryExists('client-ui');
  await runner.checkDirectoryExists('cdp-controller');
  await runner.checkDirectoryExists('docs');
  await runner.checkDirectoryExists('scripts');
});

runner.addTest('Main configuration files exist', async () => {
  await runner.checkFileExists('package.json');
  await runner.checkFileExists('README.md');
  await runner.checkFileExists('.env');
});

runner.addTest('Documentation files exist', async () => {
  await runner.checkFileExists('docs/ARCHITECTURE.md');
});

// Signaling Server Tests
runner.addTest('Signaling server files exist', async () => {
  await runner.checkFileExists('signaling-server/package.json');
  await runner.checkFileExists('signaling-server/server.js');
  await runner.checkFileExists('signaling-server/browser-manager.js');
  await runner.checkFileExists('signaling-server/webrtc-signaling.js');
});

runner.addTest('Signaling server package.json is valid', async () => {
  await runner.checkPackageJson('signaling-server');
});

// CDP Controller Tests
runner.addTest('CDP controller files exist', async () => {
  await runner.checkFileExists('cdp-controller/manager.js');
  await runner.checkFileExists('cdp-controller/screencast.js');
  await runner.checkFileExists('cdp-controller/webrtc-client.js');
});

// Client UI Tests
runner.addTest('Client UI files exist', async () => {
  await runner.checkFileExists('client-ui/package.json');
  await runner.checkFileExists('client-ui/index.html');
  await runner.checkFileExists('client-ui/app.js');
  await runner.checkFileExists('client-ui/webrtc-handler.js');
  await runner.checkFileExists('client-ui/style.css');
});

runner.addTest('Client UI package.json is valid', async () => {
  await runner.checkPackageJson('client-ui');
});

// Port Availability Tests
runner.addTest('Required ports are available', async () => {
  const ports = [3000, 8080, 9222];
  
  for (const port of ports) {
    const available = await runner.testPortAvailable(port);
    if (!available) {
      throw new Error(`Port ${port} is not available`);
    }
  }
});

// Dependency Tests
runner.addTest('Node.js version is compatible', async () => {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    throw new Error(`Node.js 18+ required, found ${nodeVersion}`);
  }
});

runner.addTest('Root dependencies can be installed', async () => {
  try {
    await runner.runCommand('npm install --dry-run');
  } catch (error) {
    throw new Error('Failed to validate root dependencies');
  }
});

runner.addTest('Signaling server dependencies can be installed', async () => {
  try {
    await runner.runCommand('npm install --dry-run', 
      path.join(runner.projectRoot, 'signaling-server'));
  } catch (error) {
    throw new Error('Failed to validate signaling server dependencies');
  }
});

// Chrome Tests
runner.addTest('Chrome browser is available', async () => {
  const chromeCommands = [
    'google-chrome --version',
    'google-chrome-stable --version',
    'chromium --version',
    'chromium-browser --version'
  ];
  
  let chromeFound = false;
  
  for (const cmd of chromeCommands) {
    try {
      await runner.runCommand(cmd);
      chromeFound = true;
      break;
    } catch (error) {
      // Continue trying other commands
    }
  }
  
  // Check macOS Chrome
  if (!chromeFound) {
    const macChrome = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome';
    if (fs.existsSync(macChrome)) {
      chromeFound = true;
    }
  }
  
  if (!chromeFound) {
    throw new Error('Chrome browser not found');
  }
});

// Script Tests
runner.addTest('Setup script exists and is executable', async () => {
  await runner.checkFileExists('scripts/setup.js');
  await runner.checkFileExists('scripts/start.js');
  
  const setupPath = path.join(runner.projectRoot, 'scripts/setup.js');
  const stats = fs.statSync(setupPath);
  
  // Check if file is executable (on Unix systems)
  if (process.platform !== 'win32') {
    const isExecutable = !!(stats.mode & parseInt('111', 8));
    if (!isExecutable) {
      throw new Error('Setup script is not executable');
    }
  }
});

// Configuration Tests
runner.addTest('Environment configuration is valid', async () => {
  const envPath = path.join(runner.projectRoot, '.env');
  if (!fs.existsSync(envPath)) {
    throw new Error('.env file not found');
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = ['CDP_PORT', 'SIGNALING_PORT', 'CLIENT_PORT'];
  
  for (const varName of requiredVars) {
    if (!envContent.includes(varName)) {
      throw new Error(`Missing environment variable: ${varName}`);
    }
  }
});

// Run all tests
runner.runTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});

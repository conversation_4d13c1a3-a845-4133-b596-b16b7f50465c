{"name": "session-inject", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start-servers": "node postmessage-demos/servers/start-servers.js", "server1": "node postmessage-demos/servers/server1.js", "server2": "node postmessage-demos/servers/server2.js", "cdp-monitor": "node postmessage-demos/cdp-monitoring/monitor.js"}, "dependencies": {"puppeteer": "^24.10.0", "ws": "^8.18.3"}, "author": "", "license": "ISC"}
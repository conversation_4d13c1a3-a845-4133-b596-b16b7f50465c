import http from "http";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { URL } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = 3002;
const STATIC_DIR = path.join(__dirname, "..");

// MIME types
const mimeTypes = {
  ".html": "text/html",
  ".js": "text/javascript",
  ".css": "text/css",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".gif": "image/gif",
  ".ico": "image/x-icon",
};

const server = http.createServer((req, res) => {
  // Add CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Add security headers - allow framing from localhost:3001 for iframe examples
  res.setHeader("X-Frame-Options", "ALLOW-FROM http://localhost:3001");
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-XSS-Protection", "1; mode=block");

  if (req.method === "OPTIONS") {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = new URL(req.url, `http://localhost:${PORT}`);
  let pathname = parsedUrl.pathname;

  // Root route
  if (pathname === "/") {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
      <h1>PostMessage Demo Server 2 (Origin 2)</h1>
      <p>Running on port ${PORT}</p>
      <p>This server provides the secondary origin for cross-origin testing.</p>
      <h2>Available Examples:</h2>
      <ul>
        <li><a href="/cross-origin-iframe/iframe.html">Cross-Origin Iframe Content</a></li>
        <li><a href="/inter-tab/tab2.html">Inter-Tab Communication (Tab 2)</a></li>
      </ul>
      <p><strong>Note:</strong> Most examples should be accessed from Server 1 (port 3001)</p>
    `);
    return;
  }

  // Serve static files
  const filePath = path.join(STATIC_DIR, pathname);
  const ext = path.extname(filePath);
  const contentType = mimeTypes[ext] || "text/plain";

  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { "Content-Type": "text/html" });
      res.end("<h1>404 Not Found</h1>");
      return;
    }
    res.writeHead(200, { "Content-Type": contentType });
    res.end(data);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 Server 2 (Origin 2) running at http://localhost:${PORT}`);
  console.log(`📁 Serving files from: ${STATIC_DIR}`);
});

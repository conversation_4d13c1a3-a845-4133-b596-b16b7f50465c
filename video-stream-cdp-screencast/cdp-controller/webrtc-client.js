/**
 * WebRTC Client Module for CDP Controller
 * 
 * Handles WebRTC peer connections for streaming screencast data
 * from the CDP manager to client UIs.
 */

class WebRTCClient {
  constructor(signalingSocket) {
    this.signalingSocket = signalingSocket;
    this.debug = true;
    
    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.cloudflare.com:3478' },
        { urls: 'stun:stun.l.google.com:19302' }
      ],
      iceCandidatePoolSize: 10
    };
    
    // Connection management
    this.peerConnections = new Map(); // clientId -> RTCPeerConnection
    this.dataChannels = new Map(); // clientId -> RTCDataChannel
    this.connectionStates = new Map(); // clientId -> state info
    
    // Data handling
    this.frameCallback = null;
    this.messageHandlers = new Map();
    
    this.setupMessageHandlers();
  }

  setupMessageHandlers() {
    this.messageHandlers.set('webrtc-offer', this.handleOffer.bind(this));
    this.messageHandlers.set('webrtc-answer', this.handleAnswer.bind(this));
    this.messageHandlers.set('webrtc-ice-candidate', this.handleIceCandidate.bind(this));
  }

  async createPeerConnection(clientId) {
    this.log(`Creating peer connection for client: ${clientId}`);
    
    const pc = new RTCPeerConnection(this.rtcConfig);
    
    // Store connection
    this.peerConnections.set(clientId, pc);
    this.connectionStates.set(clientId, {
      state: 'initializing',
      createdAt: Date.now(),
      lastActivity: Date.now()
    });

    // Create data channel for screencast frames
    const dataChannel = pc.createDataChannel('screencast', {
      ordered: true,
      maxRetransmits: 3
    });

    this.dataChannels.set(clientId, dataChannel);

    // Set up event handlers
    this.setupPeerConnectionHandlers(pc, dataChannel, clientId);
    
    return pc;
  }

  setupPeerConnectionHandlers(pc, dataChannel, clientId) {
    // Connection state monitoring
    pc.onconnectionstatechange = () => {
      const state = pc.connectionState;
      this.log(`WebRTC connection state for ${clientId}: ${state}`);
      
      this.updateConnectionState(clientId, { state });
      
      if (state === 'failed' || state === 'closed') {
        this.cleanupConnection(clientId);
      }
    };

    // ICE connection state
    pc.oniceconnectionstatechange = () => {
      this.log(`ICE connection state for ${clientId}: ${pc.iceConnectionState}`);
    };

    // ICE candidate handling
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalingMessage({
          type: 'webrtc-ice-candidate',
          candidate: event.candidate,
          targetClientId: clientId
        });
      }
    };

    // Data channel handlers
    dataChannel.onopen = () => {
      this.log(`Data channel opened for client: ${clientId}`);
      this.updateConnectionState(clientId, { dataChannelOpen: true });
    };

    dataChannel.onclose = () => {
      this.log(`Data channel closed for client: ${clientId}`);
      this.updateConnectionState(clientId, { dataChannelOpen: false });
    };

    dataChannel.onerror = (error) => {
      this.error(`Data channel error for ${clientId}:`, error);
    };

    dataChannel.onmessage = (event) => {
      this.handleDataChannelMessage(clientId, event.data);
    };
  }

  async createOffer(clientId) {
    const pc = this.peerConnections.get(clientId);
    if (!pc) {
      throw new Error(`No peer connection found for client: ${clientId}`);
    }

    this.log(`Creating offer for client: ${clientId}`);

    try {
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      this.sendSignalingMessage({
        type: 'webrtc-offer',
        offer: offer,
        targetClientId: clientId
      });

      this.updateConnectionState(clientId, { offerSent: true });
      
      return offer;
    } catch (error) {
      this.error(`Failed to create offer for ${clientId}:`, error);
      throw error;
    }
  }

  async handleOffer(message) {
    // In our architecture, the manager creates offers, not receives them
    // This would be used if clients initiated connections
    this.log('Received offer (not expected in current architecture)');
  }

  async handleAnswer(message) {
    const clientId = message.fromClientId;
    const pc = this.peerConnections.get(clientId);
    
    if (!pc) {
      this.error(`No peer connection found for answer from: ${clientId}`);
      return;
    }

    this.log(`Handling answer from client: ${clientId}`);

    try {
      await pc.setRemoteDescription(new RTCSessionDescription(message.answer));
      this.updateConnectionState(clientId, { answerReceived: true });
    } catch (error) {
      this.error(`Failed to handle answer from ${clientId}:`, error);
    }
  }

  async handleIceCandidate(message) {
    const clientId = message.fromClientId;
    const pc = this.peerConnections.get(clientId);
    
    if (!pc) {
      this.error(`No peer connection found for ICE candidate from: ${clientId}`);
      return;
    }

    try {
      await pc.addIceCandidate(new RTCIceCandidate(message.candidate));
      this.updateConnectionState(clientId, { lastActivity: Date.now() });
    } catch (error) {
      this.error(`Failed to add ICE candidate from ${clientId}:`, error);
    }
  }

  handleDataChannelMessage(clientId, data) {
    try {
      const message = JSON.parse(data);
      this.log(`Data channel message from ${clientId}:`, message.type);
      
      // Handle control messages from client
      switch (message.type) {
        case 'qualityRequest':
          this.handleQualityRequest(clientId, message);
          break;
        case 'ping':
          this.sendDataChannelMessage(clientId, { type: 'pong', timestamp: Date.now() });
          break;
        default:
          this.log(`Unknown data channel message type: ${message.type}`);
      }
    } catch (error) {
      this.error(`Error parsing data channel message from ${clientId}:`, error);
    }
  }

  handleQualityRequest(clientId, message) {
    // Forward quality adjustment request to screencast module
    if (this.frameCallback) {
      this.frameCallback({
        type: 'qualityAdjustment',
        clientId: clientId,
        quality: message.quality,
        resolution: message.resolution
      });
    }
  }

  sendScreencastFrame(frameData) {
    // Send frame to all connected clients
    for (const [clientId, dataChannel] of this.dataChannels) {
      if (dataChannel.readyState === 'open') {
        try {
          const message = {
            type: 'screencastFrame',
            ...frameData,
            timestamp: Date.now()
          };
          
          dataChannel.send(JSON.stringify(message));
          this.updateConnectionState(clientId, { 
            lastFrameSent: Date.now(),
            framesSent: (this.connectionStates.get(clientId)?.framesSent || 0) + 1
          });
          
        } catch (error) {
          this.error(`Failed to send frame to ${clientId}:`, error);
        }
      }
    }
  }

  sendDataChannelMessage(clientId, message) {
    const dataChannel = this.dataChannels.get(clientId);
    if (dataChannel && dataChannel.readyState === 'open') {
      try {
        dataChannel.send(JSON.stringify(message));
      } catch (error) {
        this.error(`Failed to send data channel message to ${clientId}:`, error);
      }
    }
  }

  broadcastDataChannelMessage(message) {
    for (const [clientId, dataChannel] of this.dataChannels) {
      if (dataChannel.readyState === 'open') {
        this.sendDataChannelMessage(clientId, message);
      }
    }
  }

  updateConnectionState(clientId, updates) {
    const currentState = this.connectionStates.get(clientId) || {};
    this.connectionStates.set(clientId, { ...currentState, ...updates });
  }

  cleanupConnection(clientId) {
    this.log(`Cleaning up connection for client: ${clientId}`);
    
    const pc = this.peerConnections.get(clientId);
    if (pc) {
      pc.close();
      this.peerConnections.delete(clientId);
    }
    
    this.dataChannels.delete(clientId);
    this.connectionStates.delete(clientId);
  }

  cleanupAllConnections() {
    this.log('Cleaning up all WebRTC connections');
    
    for (const clientId of this.peerConnections.keys()) {
      this.cleanupConnection(clientId);
    }
  }

  sendSignalingMessage(message) {
    if (this.signalingSocket && this.signalingSocket.readyState === WebSocket.OPEN) {
      this.signalingSocket.send(JSON.stringify(message));
    }
  }

  handleSignalingMessage(message) {
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    }
  }

  setFrameCallback(callback) {
    this.frameCallback = callback;
  }

  getConnectionStats() {
    const stats = {
      totalConnections: this.peerConnections.size,
      activeDataChannels: 0,
      connections: {}
    };

    for (const [clientId, state] of this.connectionStates) {
      const dataChannel = this.dataChannels.get(clientId);
      const isDataChannelOpen = dataChannel && dataChannel.readyState === 'open';
      
      if (isDataChannelOpen) {
        stats.activeDataChannels++;
      }

      stats.connections[clientId] = {
        ...state,
        dataChannelOpen: isDataChannelOpen,
        dataChannelState: dataChannel?.readyState || 'none'
      };
    }

    return stats;
  }

  log(...args) {
    if (this.debug) {
      console.log('[WebRTCClient]', ...args);
    }
  }

  error(...args) {
    console.error('[WebRTCClient ERROR]', ...args);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebRTCClient;
} else if (typeof window !== 'undefined') {
  window.WebRTCClient = WebRTCClient;
}

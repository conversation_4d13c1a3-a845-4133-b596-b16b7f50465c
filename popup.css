/* Popup styles for Video Stream Extension */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 400px;
    min-height: 600px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4757;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #2ed573;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    padding: 16px;
}

section {
    margin-bottom: 20px;
}

section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

/* Tab Information */
.tab-details {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.tab-url {
    font-size: 12px;
    color: #666;
    word-break: break-all;
    margin-bottom: 4px;
}

.tab-title {
    font-weight: 500;
    color: #333;
}

/* Video Preview */
.video-container {
    position: relative;
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    aspect-ratio: 16/9;
}

#previewVideo {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    text-align: center;
}

.video-overlay.hidden {
    display: none;
}

/* Controls */
.control-group {
    margin-bottom: 12px;
}

.control-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #555;
}

.control-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;
}

.control-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
    margin-bottom: 4px;
}

.btn:last-child {
    margin-right: 0;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
}

/* Stream Information */
.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.info-item {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
}

.info-item label {
    font-weight: 500;
    color: #666;
    display: block;
    margin-bottom: 2px;
}

.info-item span {
    color: #333;
    font-weight: 600;
}

/* Logs */
.log-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    max-height: 120px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

.log-entry {
    margin-bottom: 4px;
    color: #666;
}

.log-entry:last-child {
    margin-bottom: 0;
}

.log-entry.error {
    color: #dc3545;
}

.log-entry.success {
    color: #28a745;
}

.log-entry.info {
    color: #17a2b8;
}

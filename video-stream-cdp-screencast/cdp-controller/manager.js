/**
 * CDP Controller Script - Browser-side Manager
 *
 * This script gets injected into a dedicated "manager tab" within the controlled browser instance.
 * It connects to Chrome DevTools Protocol and manages screencast streaming via WebRTC.
 */

(function () {
  "use strict";

  class CDPManager {
    constructor() {
      this.debug = true;
      this.cdpPort = 9222;
      this.signalingServerUrl = "ws://localhost:3000";

      // CDP connection
      this.cdpWebSocket = null;
      this.cdpConnected = false;
      this.messageId = 1;
      this.pendingMessages = new Map();

      // Target management
      this.availableTargets = new Map();
      this.currentTarget = null;
      this.screencastActive = false;

      // WebRTC connection
      this.peerConnection = null;
      this.dataChannel = null;
      this.signalingSocket = null;
      this.clientId = null;

      // Screencast settings
      this.screencastConfig = {
        format: "jpeg",
        quality: 80,
        maxWidth: 1280,
        maxHeight: 720,
        everyNthFrame: 1,
      };

      this.init();
    }

    async init() {
      this.log("Initializing CDP Manager...");

      try {
        // Connect to signaling server first
        await this.connectToSignalingServer();

        // Then connect to CDP
        await this.connectToCDP();

        // Discover available targets
        await this.discoverTargets();

        this.log("CDP Manager initialized successfully");

        // Notify signaling server that manager is ready
        this.sendToSignalingServer({
          type: "managerReady",
          capabilities: {
            screencast: true,
            webrtc: true,
            targets: this.availableTargets.size,
          },
        });
      } catch (error) {
        this.error("Failed to initialize CDP Manager:", error);
      }
    }

    async connectToSignalingServer() {
      return new Promise((resolve, reject) => {
        this.log("Connecting to signaling server...");

        this.signalingSocket = new WebSocket(this.signalingServerUrl);

        this.signalingSocket.onopen = () => {
          this.log("Connected to signaling server");

          // Identify as manager
          this.sendToSignalingServer({
            type: "identify",
            clientType: "manager",
            userAgent: navigator.userAgent,
          });

          resolve();
        };

        this.signalingSocket.onmessage = (event) => {
          this.handleSignalingMessage(JSON.parse(event.data));
        };

        this.signalingSocket.onclose = () => {
          this.log("Signaling server connection closed");
          // Attempt reconnection
          setTimeout(() => this.connectToSignalingServer(), 5000);
        };

        this.signalingSocket.onerror = (error) => {
          this.error("Signaling server connection error:", error);
          reject(error);
        };
      });
    }

    async connectToCDP() {
      return new Promise((resolve, reject) => {
        this.log("Connecting to CDP...");

        // First, get the browser WebSocket endpoint
        fetch(`http://localhost:${this.cdpPort}/json/version`)
          .then((response) => response.json())
          .then((data) => {
            const wsUrl = data.webSocketDebuggerUrl;
            this.log("CDP WebSocket URL:", wsUrl);

            this.cdpWebSocket = new WebSocket(wsUrl);

            this.cdpWebSocket.onopen = () => {
              this.log("Connected to CDP");
              this.cdpConnected = true;
              resolve();
            };

            this.cdpWebSocket.onmessage = (event) => {
              this.handleCDPMessage(JSON.parse(event.data));
            };

            this.cdpWebSocket.onclose = () => {
              this.log("CDP connection closed");
              this.cdpConnected = false;
            };

            this.cdpWebSocket.onerror = (error) => {
              this.error("CDP connection error:", error);
              reject(error);
            };
          })
          .catch(reject);
      });
    }

    async discoverTargets() {
      this.log("Discovering targets...");
      if (!this.cdpConnected) return;
      const response = await this.sendCDPCommand("Target.getTargets");

      if (response && response.result && response.result.targetInfos) {
        const targets = response.result.targetInfos
          .filter(
            (target) => target.type === "page" && target.url !== "about:blank"
          )
          .map((target) => ({
            id: target.targetId,
            title: target.title,
            url: target.url,
            type: target.type,
            favicon: this.getFaviconUrl(target.url),
          }));

        this.availableTargets.clear();
        targets.forEach((target) => {
          this.availableTargets.set(target.id, target);
        });

        this.log(`Discovered targets`, targets);

        // Send targets to signaling server
        this.sendToSignalingServer({
          type: "targetsDiscovered",
          targets: targets,
        });
      }
    }

    async sendCDPCommand(method, params = {}) {
      if (!this.cdpConnected) {
        throw new Error("CDP not connected");
      }

      const id = this.messageId++;
      const message = { id, method, params };

      return new Promise((resolve, reject) => {
        this.pendingMessages.set(id, { resolve, reject });
        this.cdpWebSocket.send(JSON.stringify(message));

        // Timeout after 10 seconds
        setTimeout(() => {
          if (this.pendingMessages.has(id)) {
            this.pendingMessages.delete(id);
            reject(new Error(`CDP command timeout: ${method}`));
          }
        }, 10000);
      });
    }

    handleCDPMessage(message) {
      if (message.id && this.pendingMessages.has(message.id)) {
        const { resolve, reject } = this.pendingMessages.get(message.id);
        this.pendingMessages.delete(message.id);

        if (message.error) {
          reject(new Error(message.error.message));
        } else {
          resolve(message);
        }
      } else if (message.method) {
        // Handle CDP events
        this.handleCDPEvent(message.method, message.params);
      }
    }

    handleCDPEvent(method, params) {
      switch (method) {
        case "Page.screencastFrame":
          this.handleScreencastFrame(params);
          break;
        case "Target.targetCreated":
        case "Target.targetDestroyed":
        case "Target.targetInfoChanged":
          // Re-discover targets when they change
          this.discoverTargets();
          break;
      }
    }

    handleScreencastFrame(params) {
      if (!this.peerConnection || !this.dataChannel) return;

      try {
        // Send frame data via WebRTC data channel
        const frameData = {
          type: "screencastFrame",
          data: params.data,
          metadata: {
            sessionId: params.sessionId,
            timestamp: Date.now(),
          },
        };

        if (this.dataChannel.readyState === "open") {
          this.dataChannel.send(JSON.stringify(frameData));
        }

        // Acknowledge the frame
        this.sendCDPCommand("Page.screencastFrameAck", {
          sessionId: params.sessionId,
        });
      } catch (error) {
        this.error("Error handling screencast frame:", error);
      }
    }

    handleSignalingMessage(message) {
      this.log("Signaling message:", message.type);

      switch (message.type) {
        case "welcome":
          this.clientId = message.clientId;
          break;

        case "selectTarget":
          this.handleTargetSelection(message);
          break;

        case "webrtc-offer":
          this.handleWebRTCOffer(message);
          break;

        case "webrtc-ice-candidate":
          this.handleWebRTCIceCandidate(message);
          break;

        case "discoverTargets":
          this.discoverTargets();
          break;
      }
    }

    async handleTargetSelection(message) {
      this.log(`Target selected: ${message.targetId}`);

      try {
        // Stop current screencast if active
        if (this.screencastActive) {
          await this.stopScreencast();
        }

        // Start screencast on new target
        await this.startScreencast(message.targetId);

        // Initialize WebRTC connection with client
        await this.initializeWebRTC(message.clientId);
      } catch (error) {
        this.error("Error handling target selection:", error);
      }
    }

    async startScreencast(targetId) {
      this.log(`Starting screencast for target: ${targetId}`);

      try {
        // Attach to target
        const attachResponse = await this.sendCDPCommand(
          "Target.attachToTarget",
          {
            targetId: targetId,
            flatten: true,
          }
        );

        const sessionId = attachResponse.result.sessionId;

        // Enable Page domain
        await this.sendCDPCommand("Page.enable", {}, sessionId);

        // Start screencast
        await this.sendCDPCommand(
          "Page.startScreencast",
          this.screencastConfig,
          sessionId
        );

        this.currentTarget = targetId;
        this.screencastActive = true;

        this.log("Screencast started successfully");
      } catch (error) {
        this.error("Failed to start screencast:", error);
        throw error;
      }
    }

    async stopScreencast() {
      if (!this.screencastActive || !this.currentTarget) return;

      this.log("Stopping screencast...");

      try {
        await this.sendCDPCommand("Page.stopScreencast");
        this.screencastActive = false;
        this.currentTarget = null;

        this.log("Screencast stopped");
      } catch (error) {
        this.error("Error stopping screencast:", error);
      }
    }

    async initializeWebRTC(clientId) {
      this.log("Initializing WebRTC connection...");

      // Create peer connection
      this.peerConnection = new RTCPeerConnection({
        iceServers: [
          { urls: "stun:stun.cloudflare.com:3478" },
          { urls: "stun:stun.l.google.com:19302" },
        ],
      });

      // Create data channel for screencast frames
      this.dataChannel = this.peerConnection.createDataChannel("screencast", {
        ordered: true,
      });

      this.dataChannel.onopen = () => {
        this.log("Data channel opened");
      };

      this.dataChannel.onclose = () => {
        this.log("Data channel closed");
      };

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendToSignalingServer({
            type: "webrtc-ice-candidate",
            candidate: event.candidate,
            targetClientId: clientId,
          });
        }
      };

      // Create offer
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);

      // Send offer to client
      this.sendToSignalingServer({
        type: "webrtc-offer",
        offer: offer,
        targetClientId: clientId,
      });
    }

    async handleWebRTCOffer(message) {
      // This would be for receiving offers from clients
      // In our case, the manager creates offers
    }

    async handleWebRTCIceCandidate(message) {
      if (this.peerConnection && message.candidate) {
        try {
          await this.peerConnection.addIceCandidate(
            new RTCIceCandidate(message.candidate)
          );
        } catch (error) {
          this.error("Error adding ICE candidate:", error);
        }
      }
    }

    sendToSignalingServer(message) {
      if (
        this.signalingSocket &&
        this.signalingSocket.readyState === WebSocket.OPEN
      ) {
        this.signalingSocket.send(JSON.stringify(message));
      }
    }

    getFaviconUrl(url) {
      try {
        const urlObj = new URL(url);
        return `${urlObj.origin}/favicon.ico`;
      } catch {
        return "";
      }
    }

    log(...args) {
      if (this.debug) {
        console.log("[CDPManager]", ...args);
      }
    }

    error(...args) {
      console.error("[CDPManager ERROR]", ...args);
    }
  }

  // Initialize CDP Manager when script loads
  if (typeof window !== "undefined") {
    window.cdpManager = new CDPManager();
  }
})();

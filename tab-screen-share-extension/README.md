# Tab Screen Share Extension

A simple Chrome extension that captures the active tab's screen content using Chrome's `tabCapture` API.

## Features

- **Simple Tab Capture**: Capture audio and video from the current active tab
- **Real-time Preview**: See the captured content in the extension popup
- **Easy Controls**: Start and stop capture with simple buttons
- **Status Monitoring**: View capture status, duration, and track information
- **Clean Interface**: Minimal, focused UI for ease of use

## Installation

1. **Download/Clone** the extension files
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer mode** (toggle in top right)
4. **Click "Load unpacked"** and select the `tab-screen-share-extension` directory
5. **Pin the extension** to your toolbar for easy access

## Usage

### Testing the Extension

1. **Open the test page**: Open `test-page.html` in a new tab (or navigate to any webpage with content)
2. **Load the extension**: Make sure the extension is loaded in chrome://extensions/
3. **Test capture**: Follow the steps below

### Starting Capture

1. **Navigate** to the tab you want to capture (try the test-page.html first)
2. **Click** the Tab Screen Share extension icon
3. **Click "Start Capture"** button
4. **Grant permissions** when prompted by Chrome
5. **View** the capture status and preview in the popup

### Debugging Steps

If capture fails:

1. **Check the browser console**: Open DevTools (F12) and check for errors
2. **Check extension console**: Go to chrome://extensions/ → Extension details → "Inspect views: background page"
3. **Try the test page**: Use the included test-page.html which has visual and audio content
4. **Check permissions**: Ensure the extension has tabCapture permissions

### Stopping Capture

1. **Click** the extension icon
2. **Click "Stop Capture"** button
3. **Capture will stop** and resources will be cleaned up

### Monitoring

The extension popup shows:

- Current tab information (URL and title)
- Capture status (active/inactive)
- Capture duration
- Number of audio and video tracks
- Activity log with timestamps

## Technical Details

### Permissions

The extension requires these permissions:

- `activeTab`: Access to the current active tab
- `tabCapture`: Ability to capture tab audio and video

### API Usage

- **chrome.tabCapture.capture()**: Captures the active tab's media stream
- **chrome.tabs.query()**: Gets information about the active tab
- **MediaStream API**: Handles the captured audio/video streams

### Browser Support

- **Chrome 88+**: Full support for tabCapture API
- **Chromium-based browsers**: Should work with compatible versions

## Limitations

### Chrome API Limitations

- **User Interaction Required**: Capture must be initiated by user action
- **Active Tab Only**: Can only capture the currently active tab
- **Permission Prompts**: Chrome may show permission dialogs
- **Tab Focus**: Capture may pause if tab loses focus

### Technical Constraints

- **Audio/Video Quality**: Limited by Chrome's tabCapture implementation
- **Performance**: Capture uses system resources (CPU, memory)
- **Network Content**: Some content may not be capturable due to security policies

## Troubleshooting

### Common Issues

1. **"Capture failed" error**

   - Ensure the tab has audio/video content
   - Try refreshing the tab and starting capture again
   - Check that no other extension is capturing the tab

2. **No audio in capture**

   - Verify the tab is playing audio
   - Check browser audio settings
   - Some sites may block audio capture

3. **Extension not working**
   - Reload the extension in chrome://extensions/
   - Check browser console for errors
   - Ensure you're on a supported Chrome version

### Debug Information

To debug issues:

1. Open the extension popup
2. Check the activity log for error messages
3. Open Chrome DevTools on the popup (right-click → Inspect)
4. Check the background script console in chrome://extensions/

## Development

### File Structure

```
tab-screen-share-extension/
├── manifest.json          # Extension configuration
├── background.js          # Service worker (main logic)
├── popup.html            # Extension popup interface
├── popup.css             # Popup styling
├── popup.js              # Popup functionality
├── icon.png              # Extension icon (placeholder)
└── README.md             # This file
```

### Key Components

1. **Background Script** (`background.js`)

   - Handles tabCapture API calls
   - Manages active capture sessions
   - Provides message handling for popup communication

2. **Popup Interface** (`popup.html/css/js`)
   - User interface for starting/stopping capture
   - Real-time status display
   - Activity logging

### Extending the Extension

To add new features:

1. **Modify manifest.json** for additional permissions
2. **Extend background.js** for new capture functionality
3. **Update popup files** for UI changes
4. **Test thoroughly** with different tab content types

## Privacy and Security

### Data Handling

- **No Data Storage**: Extension doesn't store captured content
- **Local Processing**: All capture happens locally in the browser
- **No Network Transmission**: Captured streams are not sent anywhere

### Permissions Usage

- **activeTab**: Only used to identify the current tab for capture
- **tabCapture**: Only used when user explicitly starts capture
- **No Broad Access**: Extension cannot access other tabs or browser data

## License

MIT License - Feel free to modify and distribute.

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review the browser console for error messages
3. Ensure you're using a supported Chrome version
4. Try reloading the extension if issues persist

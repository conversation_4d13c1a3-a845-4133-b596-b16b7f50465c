// Video Capture System for Chrome Extension
class VideoCaptureManager {
  constructor() {
    this.activeCaptures = new Map();
    this.captureConstraints = {
      video: {
        mediaSource: 'tab',
        width: { ideal: 1920, max: 1920 },
        height: { ideal: 1080, max: 1080 },
        frameRate: { ideal: 30, max: 30 }
      },
      audio: {
        mandatory: {
          chromeMediaSource: 'tab',
          echoCancellation: true
        }
      }
    };
  }

  async startTabCapture(tabId, options = {}) {
    try {
      // Check if already capturing this tab
      if (this.activeCaptures.has(tabId)) {
        throw new Error('Tab is already being captured');
      }

      // Request desktop capture stream ID
      const streamId = await this.requestCapturePermission();
      
      // Get the media stream using the stream ID
      const stream = await this.getMediaStream(streamId, options);
      
      // Store capture info
      const captureInfo = {
        tabId,
        streamId,
        stream,
        startTime: Date.now(),
        options: { ...this.captureConstraints, ...options }
      };
      
      this.activeCaptures.set(tabId, captureInfo);
      
      // Set up stream event listeners
      this.setupStreamListeners(stream, tabId);
      
      console.log(`Started video capture for tab ${tabId}`);
      return captureInfo;
      
    } catch (error) {
      console.error(`Failed to start tab capture for ${tabId}:`, error);
      throw new Error(`Video capture failed: ${error.message}`);
    }
  }

  async requestCapturePermission() {
    return new Promise((resolve, reject) => {
      chrome.desktopCapture.chooseDesktopMedia(
        ['tab', 'audio'],
        (streamId) => {
          if (streamId) {
            resolve(streamId);
          } else {
            reject(new Error('User cancelled capture or permission denied'));
          }
        }
      );
    });
  }

  async getMediaStream(streamId, options = {}) {
    const constraints = {
      video: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: streamId,
          maxWidth: options.maxWidth || 1920,
          maxHeight: options.maxHeight || 1080,
          maxFrameRate: options.maxFrameRate || 30
        }
      },
      audio: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: streamId,
          echoCancellation: true
        }
      }
    };

    // Remove audio if not requested
    if (options.audioDisabled) {
      delete constraints.audio;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      return stream;
    } catch (error) {
      throw new Error(`Failed to get media stream: ${error.message}`);
    }
  }

  setupStreamListeners(stream, tabId) {
    // Listen for track events
    stream.getTracks().forEach(track => {
      track.addEventListener('ended', () => {
        console.log(`Track ended for tab ${tabId}:`, track.kind);
        this.handleTrackEnded(tabId, track);
      });

      track.addEventListener('mute', () => {
        console.log(`Track muted for tab ${tabId}:`, track.kind);
      });

      track.addEventListener('unmute', () => {
        console.log(`Track unmuted for tab ${tabId}:`, track.kind);
      });
    });
  }

  handleTrackEnded(tabId, track) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (captureInfo) {
      // Check if all tracks have ended
      const activeTracks = captureInfo.stream.getTracks().filter(t => t.readyState === 'live');
      if (activeTracks.length === 0) {
        console.log(`All tracks ended for tab ${tabId}, stopping capture`);
        this.stopCapture(tabId);
      }
    }
  }

  async stopCapture(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      return false;
    }

    try {
      // Stop all tracks
      captureInfo.stream.getTracks().forEach(track => {
        track.stop();
      });

      // Remove from active captures
      this.activeCaptures.delete(tabId);
      
      console.log(`Stopped video capture for tab ${tabId}`);
      return true;
    } catch (error) {
      console.error(`Error stopping capture for tab ${tabId}:`, error);
      return false;
    }
  }

  getActiveCapture(tabId) {
    return this.activeCaptures.get(tabId);
  }

  getAllActiveCaptures() {
    return Array.from(this.activeCaptures.values());
  }

  isCapturing(tabId) {
    return this.activeCaptures.has(tabId);
  }

  async getCaptureStats(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      return null;
    }

    const stream = captureInfo.stream;
    const videoTrack = stream.getVideoTracks()[0];
    const audioTrack = stream.getAudioTracks()[0];

    const stats = {
      tabId,
      duration: Date.now() - captureInfo.startTime,
      video: null,
      audio: null
    };

    if (videoTrack) {
      const settings = videoTrack.getSettings();
      stats.video = {
        width: settings.width,
        height: settings.height,
        frameRate: settings.frameRate,
        aspectRatio: settings.aspectRatio,
        readyState: videoTrack.readyState,
        enabled: videoTrack.enabled,
        muted: videoTrack.muted
      };
    }

    if (audioTrack) {
      const settings = audioTrack.getSettings();
      stats.audio = {
        sampleRate: settings.sampleRate,
        channelCount: settings.channelCount,
        readyState: audioTrack.readyState,
        enabled: audioTrack.enabled,
        muted: audioTrack.muted
      };
    }

    return stats;
  }

  async updateCaptureConstraints(tabId, newConstraints) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      throw new Error('No active capture for this tab');
    }

    try {
      const videoTrack = captureInfo.stream.getVideoTracks()[0];
      if (videoTrack && newConstraints.video) {
        await videoTrack.applyConstraints(newConstraints.video);
      }

      const audioTrack = captureInfo.stream.getAudioTracks()[0];
      if (audioTrack && newConstraints.audio) {
        await audioTrack.applyConstraints(newConstraints.audio);
      }

      // Update stored constraints
      captureInfo.options = { ...captureInfo.options, ...newConstraints };
      
      return true;
    } catch (error) {
      throw new Error(`Failed to update constraints: ${error.message}`);
    }
  }

  cleanup() {
    // Stop all active captures
    const tabIds = Array.from(this.activeCaptures.keys());
    tabIds.forEach(tabId => {
      this.stopCapture(tabId);
    });
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VideoCaptureManager;
} else {
  window.VideoCaptureManager = VideoCaptureManager;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Capture Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .content-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .video-container {
            text-align: center;
            margin: 20px 0;
        }

        video {
            width: 100%;
            max-width: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .animated-box {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            margin: 20px auto;
            border-radius: 50%;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-30px);
            }
            60% {
                transform: translateY(-15px);
            }
        }

        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 20px 0;
        }

        .moving-text {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: center;
            animation: colorChange 3s infinite;
        }

        @keyframes colorChange {
            0% { color: #ff6b6b; }
            25% { color: #4ecdc4; }
            50% { color: #45b7d1; }
            75% { color: #96ceb4; }
            100% { color: #ff6b6b; }
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }

        button:hover {
            background: #45a049;
        }

        .audio-controls {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tab Capture Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>This page provides visual and audio content for testing tab capture</li>
                <li>Click the Tab Screen Share extension icon</li>
                <li>Click "Start Capture" to capture this tab</li>
                <li>The extension should capture all the animated content below</li>
            </ol>
        </div>

        <div class="content-section">
            <h3>🎨 Animated Visual Content</h3>
            <div class="moving-text">This text changes colors!</div>
            <div class="animated-box"></div>
            <p>This page has animated backgrounds and elements that should be visible in the capture.</p>
        </div>

        <div class="content-section">
            <h3>🎵 Audio Content</h3>
            <div class="audio-controls">
                <button onclick="playTestTone()">Play Test Tone</button>
                <button onclick="stopTestTone()">Stop Test Tone</button>
                <p id="audioStatus">Click "Play Test Tone" to generate audio for capture testing</p>
            </div>
        </div>

        <div class="content-section">
            <h3>📺 Video Content</h3>
            <div class="video-container">
                <video id="testVideo" controls>
                    <source src="data:video/mp4;base64," type="video/mp4">
                    <p>Your browser doesn't support video playback.</p>
                </video>
                <p>Note: This is a placeholder video element. For real video testing, you can navigate to YouTube or any video site.</p>
            </div>
        </div>

        <div class="content-section">
            <h3>🔧 Debug Information</h3>
            <p><strong>Page URL:</strong> <span id="pageUrl"></span></p>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
            <p><strong>Tab Capture API Available:</strong> <span id="tabCaptureAPI"></span></p>
        </div>
    </div>

    <script>
        // Audio context for generating test tones
        let audioContext;
        let oscillator;

        function playTestTone() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime); // Low volume
                
                oscillator.start();
                
                document.getElementById('audioStatus').textContent = 'Playing 440Hz test tone...';
            } catch (error) {
                document.getElementById('audioStatus').textContent = 'Error playing audio: ' + error.message;
            }
        }

        function stopTestTone() {
            if (oscillator) {
                oscillator.stop();
                oscillator = null;
            }
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            document.getElementById('audioStatus').textContent = 'Audio stopped';
        }

        // Update debug information
        function updateDebugInfo() {
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
            
            // Check if running in extension context (this won't work, but good for debugging)
            const hasTabCaptureAPI = typeof chrome !== 'undefined' && 
                                   chrome.tabCapture && 
                                   typeof chrome.tabCapture.capture === 'function';
            document.getElementById('tabCaptureAPI').textContent = hasTabCaptureAPI ? 'Yes' : 'No (Normal - API only available to extensions)';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
            
            // Update timestamp every second
            setInterval(() => {
                document.getElementById('timestamp').textContent = new Date().toLocaleString();
            }, 1000);
        });

        // Clean up audio on page unload
        window.addEventListener('beforeunload', function() {
            stopTestTone();
        });
    </script>
</body>
</html>

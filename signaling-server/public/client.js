// Web Client for receiving video streams from Chrome Extension
class VideoStreamClient {
  constructor() {
    this.ws = null;
    this.peerConnection = null;
    this.clientId = null;
    this.isConnected = false;
    this.remoteStream = null;
    
    this.iceServers = [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ];
    
    this.setupEventListeners();
    this.log('Video stream client initialized');
  }

  setupEventListeners() {
    document.getElementById('connectBtn').addEventListener('click', () => {
      this.connect();
    });

    document.getElementById('disconnectBtn').addEventListener('click', () => {
      this.disconnect();
    });

    // Handle video metadata
    const video = document.getElementById('remoteVideo');
    video.addEventListener('loadedmetadata', () => {
      this.updateVideoInfo();
    });

    video.addEventListener('resize', () => {
      this.updateVideoInfo();
    });
  }

  async connect() {
    try {
      const serverUrl = document.getElementById('serverUrl').value;
      if (!serverUrl) {
        throw new Error('Please enter a server URL');
      }

      this.log(`Connecting to ${serverUrl}...`);
      
      this.ws = new WebSocket(serverUrl);
      
      this.ws.onopen = () => {
        this.log('Connected to signaling server', 'success');
        this.isConnected = true;
        this.updateConnectionStatus();
        this.setupPeerConnection();
      };

      this.ws.onmessage = (event) => {
        this.handleSignalingMessage(event);
      };

      this.ws.onclose = () => {
        this.log('Disconnected from signaling server');
        this.isConnected = false;
        this.updateConnectionStatus();
        this.cleanup();
      };

      this.ws.onerror = (error) => {
        this.log('WebSocket error: ' + error.message, 'error');
        this.isConnected = false;
        this.updateConnectionStatus();
      };

    } catch (error) {
      this.log(`Connection failed: ${error.message}`, 'error');
    }
  }

  disconnect() {
    this.log('Disconnecting...');
    
    if (this.ws) {
      this.ws.close();
    }
    
    this.cleanup();
  }

  cleanup() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach(track => track.stop());
      this.remoteStream = null;
    }
    
    const video = document.getElementById('remoteVideo');
    video.srcObject = null;
    
    this.showVideoOverlay();
    this.updateConnectionStatus();
    this.updateVideoInfo();
  }

  setupPeerConnection() {
    try {
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.iceServers
      });

      // Handle incoming streams
      this.peerConnection.ontrack = (event) => {
        this.log('Received remote stream', 'success');
        this.remoteStream = event.streams[0];
        
        const video = document.getElementById('remoteVideo');
        video.srcObject = this.remoteStream;
        
        this.hideVideoOverlay();
        this.updateVideoInfo();
      };

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendSignalingMessage({
            type: 'ice-candidate',
            data: event.candidate
          });
        }
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection.connectionState;
        this.log(`Peer connection state: ${state}`, state === 'connected' ? 'success' : 'info');
        document.getElementById('connectionState').textContent = state;
        
        if (state === 'failed' || state === 'disconnected') {
          this.cleanup();
        }
      };

      // Handle ICE connection state changes
      this.peerConnection.oniceconnectionstatechange = () => {
        const state = this.peerConnection.iceConnectionState;
        this.log(`ICE connection state: ${state}`, 'info');
        document.getElementById('iceConnectionState').textContent = state;
      };

      this.log('Peer connection setup complete');
    } catch (error) {
      this.log(`Failed to setup peer connection: ${error.message}`, 'error');
    }
  }

  async handleSignalingMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      switch (message.type) {
        case 'connected':
          this.clientId = message.clientId;
          document.getElementById('clientId').textContent = this.clientId;
          this.log(`Assigned client ID: ${this.clientId}`, 'success');
          break;

        case 'offer':
          await this.handleOffer(message.data);
          break;

        case 'answer':
          await this.handleAnswer(message.data);
          break;

        case 'ice-candidate':
          await this.handleIceCandidate(message.data);
          break;

        case 'extension-disconnected':
          this.log('Extension disconnected', 'error');
          this.cleanup();
          break;

        case 'error':
          this.log(`Server error: ${message.message}`, 'error');
          break;

        default:
          this.log(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      this.log(`Error handling signaling message: ${error.message}`, 'error');
    }
  }

  async handleOffer(offer) {
    try {
      if (!this.peerConnection) {
        this.setupPeerConnection();
      }

      await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
      
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);
      
      this.sendSignalingMessage({
        type: 'answer',
        data: answer
      });

      this.log('Sent answer to extension', 'success');
    } catch (error) {
      this.log(`Failed to handle offer: ${error.message}`, 'error');
    }
  }

  async handleAnswer(answer) {
    try {
      if (this.peerConnection) {
        await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
        this.log('Set remote description from answer', 'success');
      }
    } catch (error) {
      this.log(`Failed to handle answer: ${error.message}`, 'error');
    }
  }

  async handleIceCandidate(candidate) {
    try {
      if (this.peerConnection) {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      }
    } catch (error) {
      this.log(`Failed to handle ICE candidate: ${error.message}`, 'error');
    }
  }

  sendSignalingMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      this.log('Cannot send message: not connected to signaling server', 'error');
    }
  }

  updateConnectionStatus() {
    const statusIndicator = document.getElementById('connectionStatus');
    const statusText = document.getElementById('statusText');
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    
    if (this.isConnected) {
      statusIndicator.classList.add('connected');
      statusText.textContent = 'Connected';
      connectBtn.disabled = true;
      disconnectBtn.disabled = false;
    } else {
      statusIndicator.classList.remove('connected');
      statusText.textContent = 'Disconnected';
      connectBtn.disabled = false;
      disconnectBtn.disabled = true;
    }
  }

  updateVideoInfo() {
    const video = document.getElementById('remoteVideo');
    const resolutionElement = document.getElementById('videoResolution');
    
    if (video.videoWidth && video.videoHeight) {
      resolutionElement.textContent = `${video.videoWidth}x${video.videoHeight}`;
    } else {
      resolutionElement.textContent = '-';
    }
  }

  showVideoOverlay() {
    document.getElementById('videoOverlay').classList.remove('hidden');
  }

  hideVideoOverlay() {
    document.getElementById('videoOverlay').classList.add('hidden');
  }

  log(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Keep only last 50 log entries
    while (logContainer.children.length > 50) {
      logContainer.removeChild(logContainer.firstChild);
    }
    
    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Initialize client when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new VideoStreamClient();
});

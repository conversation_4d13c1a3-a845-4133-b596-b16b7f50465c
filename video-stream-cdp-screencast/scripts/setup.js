#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up CDP Screencast POC...\n');

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ Node.js 18+ is required. Current version:', nodeVersion);
  process.exit(1);
}

console.log('✅ Node.js version check passed:', nodeVersion);

// Setup directories
const projectRoot = path.join(__dirname, '..');
const components = ['signaling-server', 'client-ui', 'cdp-controller'];

console.log('\n📁 Setting up component directories...');

components.forEach(component => {
  const componentPath = path.join(projectRoot, component);
  if (!fs.existsSync(componentPath)) {
    fs.mkdirSync(componentPath, { recursive: true });
    console.log(`✅ Created directory: ${component}/`);
  } else {
    console.log(`✅ Directory exists: ${component}/`);
  }
});

// Install dependencies for each component
console.log('\n📦 Installing dependencies...');

try {
  // Install root dependencies
  console.log('Installing root dependencies...');
  execSync('npm install', { cwd: projectRoot, stdio: 'inherit' });

  // Install component dependencies if package.json exists
  components.forEach(component => {
    const componentPath = path.join(projectRoot, component);
    const packageJsonPath = path.join(componentPath, 'package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      console.log(`Installing ${component} dependencies...`);
      execSync('npm install', { cwd: componentPath, stdio: 'inherit' });
    }
  });

  console.log('✅ All dependencies installed successfully');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Check Chrome installation
console.log('\n🌐 Checking Chrome installation...');

const chromeCommands = [
  'google-chrome',
  'google-chrome-stable',
  'chromium',
  'chromium-browser',
  '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
];

let chromeFound = false;
let chromePath = '';

for (const cmd of chromeCommands) {
  try {
    if (cmd.startsWith('/Applications')) {
      // macOS specific check
      if (fs.existsSync(cmd)) {
        chromeFound = true;
        chromePath = cmd;
        break;
      }
    } else {
      execSync(`which ${cmd}`, { stdio: 'ignore' });
      chromeFound = true;
      chromePath = cmd;
      break;
    }
  } catch (error) {
    // Command not found, continue
  }
}

if (chromeFound) {
  console.log('✅ Chrome found:', chromePath);
} else {
  console.log('⚠️  Chrome not found in PATH. Please ensure Chrome is installed.');
  console.log('   Download from: https://www.google.com/chrome/');
}

// Create environment configuration
console.log('\n⚙️  Creating environment configuration...');

const envConfig = `# CDP Screencast POC Configuration
DEBUG=false
CHROME_PATH=${chromePath}
CDP_PORT=9222
SIGNALING_PORT=3000
CLIENT_PORT=8080
WEBRTC_STUN_SERVER=stun:stun.cloudflare.com:3478
`;

fs.writeFileSync(path.join(projectRoot, '.env'), envConfig);
console.log('✅ Environment configuration created');

// Create start script
console.log('\n📝 Creating start script...');

const startScript = `#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting CDP Screencast POC...\\n');

// Start signaling server
console.log('Starting signaling server...');
const server = spawn('npm', ['start'], {
  cwd: path.join(__dirname, '..', 'signaling-server'),
  stdio: 'inherit'
});

// Wait a moment then start client UI
setTimeout(() => {
  console.log('Starting client UI...');
  const client = spawn('npm', ['start'], {
    cwd: path.join(__dirname, '..', 'client-ui'),
    stdio: 'inherit'
  });

  // Handle cleanup
  process.on('SIGINT', () => {
    console.log('\\n🛑 Shutting down...');
    server.kill();
    client.kill();
    process.exit(0);
  });
}, 2000);
`;

fs.writeFileSync(path.join(projectRoot, 'scripts', 'start.js'), startScript);
fs.chmodSync(path.join(projectRoot, 'scripts', 'start.js'), '755');
console.log('✅ Start script created');

console.log('\n🎉 Setup completed successfully!');
console.log('\nNext steps:');
console.log('1. Run: npm start');
console.log('2. Open http://localhost:8080 in your browser');
console.log('3. Select a target tab to start streaming');
console.log('\nFor manual setup, see README.md');

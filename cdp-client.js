// Chrome DevTools Protocol Client for enhanced video capture
class CDPClient {
  constructor() {
    this.attachedTabs = new Map();
    this.debuggerVersion = '1.3';
  }

  async attachToTab(tabId) {
    try {
      // Check if already attached
      if (this.attachedTabs.has(tabId)) {
        return this.attachedTabs.get(tabId);
      }

      // Attach debugger to tab
      await chrome.debugger.attach({ tabId }, this.debuggerVersion);
      
      // Enable required domains
      await this.enableDomains(tabId);
      
      // Store attachment info
      const attachmentInfo = {
        tabId,
        attached: true,
        timestamp: Date.now()
      };
      
      this.attachedTabs.set(tabId, attachmentInfo);
      
      console.log(`CDP attached to tab ${tabId}`);
      return attachmentInfo;
      
    } catch (error) {
      console.error(`Failed to attach CDP to tab ${tabId}:`, error);
      throw new Error(`CDP attachment failed: ${error.message}`);
    }
  }

  async detachFromTab(tabId) {
    try {
      if (this.attachedTabs.has(tabId)) {
        await chrome.debugger.detach({ tabId });
        this.attachedTabs.delete(tabId);
        console.log(`CDP detached from tab ${tabId}`);
      }
    } catch (error) {
      console.error(`Failed to detach CDP from tab ${tabId}:`, error);
    }
  }

  async enableDomains(tabId) {
    const domains = [
      'Runtime',
      'Page',
      'DOM',
      'Network',
      'Performance'
    ];

    for (const domain of domains) {
      try {
        await chrome.debugger.sendCommand({ tabId }, `${domain}.enable`);
      } catch (error) {
        console.warn(`Failed to enable ${domain} domain:`, error);
      }
    }
  }

  async executeScript(tabId, script) {
    try {
      if (!this.attachedTabs.has(tabId)) {
        await this.attachToTab(tabId);
      }

      const result = await chrome.debugger.sendCommand(
        { tabId },
        'Runtime.evaluate',
        {
          expression: script,
          returnByValue: true,
          awaitPromise: true
        }
      );

      if (result.exceptionDetails) {
        throw new Error(`Script execution failed: ${result.exceptionDetails.text}`);
      }

      return result.result.value;
    } catch (error) {
      console.error(`Failed to execute script in tab ${tabId}:`, error);
      throw error;
    }
  }

  async getPageInfo(tabId) {
    const script = `
      (function() {
        return {
          url: window.location.href,
          title: document.title,
          videoElements: Array.from(document.querySelectorAll('video')).map((video, index) => ({
            index,
            src: video.src || video.currentSrc,
            width: video.videoWidth,
            height: video.videoHeight,
            duration: video.duration,
            paused: video.paused,
            muted: video.muted,
            volume: video.volume,
            readyState: video.readyState
          })),
          canvasElements: Array.from(document.querySelectorAll('canvas')).map((canvas, index) => ({
            index,
            width: canvas.width,
            height: canvas.height,
            id: canvas.id,
            className: canvas.className
          })),
          hasWebGL: !!window.WebGLRenderingContext,
          hasWebRTC: !!window.RTCPeerConnection,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        };
      })()
    `;

    return await this.executeScript(tabId, script);
  }

  async injectCaptureHelper(tabId) {
    const helperScript = `
      (function() {
        if (window.cdpCaptureHelper) {
          return { success: true, message: 'Helper already injected' };
        }

        window.cdpCaptureHelper = {
          captureCanvas: function(canvasIndex) {
            const canvases = document.querySelectorAll('canvas');
            if (canvasIndex >= canvases.length) {
              throw new Error('Canvas index out of range');
            }
            
            const canvas = canvases[canvasIndex];
            try {
              const stream = canvas.captureStream(30);
              return {
                success: true,
                streamId: 'canvas_' + canvasIndex,
                tracks: stream.getTracks().length
              };
            } catch (error) {
              throw new Error('Failed to capture canvas: ' + error.message);
            }
          },

          getVideoStreams: function() {
            const videos = document.querySelectorAll('video');
            const streams = [];
            
            videos.forEach((video, index) => {
              if (video.srcObject && video.srcObject.getTracks) {
                streams.push({
                  index,
                  tracks: video.srcObject.getTracks().length,
                  active: video.srcObject.active
                });
              }
            });
            
            return streams;
          },

          enhanceVideoAccess: function() {
            // Override video methods for enhanced access
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
              if (!video._cdpEnhanced) {
                video._cdpEnhanced = true;
                video._originalPlay = video.play;
                video._originalPause = video.pause;
                
                video.play = function() {
                  console.log('CDP: Video play intercepted', index);
                  return video._originalPlay.call(this);
                };
                
                video.pause = function() {
                  console.log('CDP: Video pause intercepted', index);
                  return video._originalPause.call(this);
                };
              }
            });
            
            return { enhanced: videos.length };
          }
        };

        return { success: true, message: 'CDP capture helper injected' };
      })()
    `;

    return await this.executeScript(tabId, helperScript);
  }

  async captureCanvasStream(tabId, canvasIndex) {
    const script = `
      (function() {
        if (!window.cdpCaptureHelper) {
          throw new Error('CDP capture helper not injected');
        }
        
        return window.cdpCaptureHelper.captureCanvas(${canvasIndex});
      })()
    `;

    return await this.executeScript(tabId, script);
  }

  async enhanceVideoElements(tabId) {
    const script = `
      (function() {
        if (!window.cdpCaptureHelper) {
          throw new Error('CDP capture helper not injected');
        }
        
        return window.cdpCaptureHelper.enhanceVideoAccess();
      })()
    `;

    return await this.executeScript(tabId, script);
  }

  async getPerformanceMetrics(tabId) {
    try {
      const metrics = await chrome.debugger.sendCommand(
        { tabId },
        'Performance.getMetrics'
      );
      
      return metrics.metrics.reduce((acc, metric) => {
        acc[metric.name] = metric.value;
        return acc;
      }, {});
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      return {};
    }
  }

  async monitorNetworkActivity(tabId, callback) {
    try {
      await chrome.debugger.sendCommand({ tabId }, 'Network.enable');
      
      chrome.debugger.onEvent.addListener((source, method, params) => {
        if (source.tabId === tabId && method.startsWith('Network.')) {
          callback(method, params);
        }
      });
    } catch (error) {
      console.error('Failed to monitor network activity:', error);
    }
  }

  isAttached(tabId) {
    return this.attachedTabs.has(tabId);
  }

  getAttachedTabs() {
    return Array.from(this.attachedTabs.keys());
  }

  async cleanup() {
    const tabIds = Array.from(this.attachedTabs.keys());
    for (const tabId of tabIds) {
      await this.detachFromTab(tabId);
    }
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CDPClient;
} else {
  window.CDPClient = CDPClient;
}

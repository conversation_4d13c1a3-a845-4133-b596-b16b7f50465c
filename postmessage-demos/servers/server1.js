import http from "http";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { URL } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = 3001;
const STATIC_DIR = path.join(__dirname, "..");

// MIME types
const mimeTypes = {
  ".html": "text/html",
  ".js": "text/javascript",
  ".css": "text/css",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".gif": "image/gif",
  ".ico": "image/x-icon",
};

const server = http.createServer((req, res) => {
  // Add CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

  // Add security headers
  res.setHeader("X-Frame-Options", "SAMEORIGIN");
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-XSS-Protection", "1; mode=block");

  if (req.method === "OPTIONS") {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = new URL(req.url, `http://localhost:${PORT}`);
  let pathname = parsedUrl.pathname;

  // Root route
  if (pathname === "/") {
    res.writeHead(200, { "Content-Type": "text/html" });
    res.end(`
      <h1>PostMessage Demo Server 1 (Origin 1)</h1>
      <p>Running on port ${PORT}</p>
      <h2>Available Examples:</h2>
      <ul>
        <li><a href="/cross-origin-iframe/parent.html">Cross-Origin Iframe Communication</a></li>
        <li><a href="/inter-tab/tab1.html">Inter-Tab Communication (Tab 1)</a></li>
        <li><a href="/parent-child-window/parent.html">Parent-Child Window Communication</a></li>
        <li><a href="/multi-tab-complex/tab1.html">Multi-Tab Complex (Tab 1)</a></li>
        <li><a href="/multi-tab-complex/tab2.html">Multi-Tab Complex (Tab 2)</a></li>
      </ul>
    `);
    return;
  }

  // Serve static files
  const filePath = path.join(STATIC_DIR, pathname);
  const ext = path.extname(filePath);
  const contentType = mimeTypes[ext] || "text/plain";

  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { "Content-Type": "text/html" });
      res.end("<h1>404 Not Found</h1>");
      return;
    }
    res.writeHead(200, { "Content-Type": contentType });
    res.end(data);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 Server 1 (Origin 1) running at http://localhost:${PORT}`);
  console.log(`📁 Serving files from: ${STATIC_DIR}`);
});

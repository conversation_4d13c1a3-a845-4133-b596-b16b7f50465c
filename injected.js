// Injected script for enhanced page access
(function () {
  "use strict";

  class InjectedVideoCapture {
    constructor() {
      this.captureStream = null;
      this.setupPageAccess();
    }

    setupPageAccess() {
      // Enhanced access to page resources
      // this.monitorVideoElements();
      // this.setupCanvasCapture();
    }

    monitorVideoElements() {
      // Monitor video elements for changes
      const videos = document.querySelectorAll("video");
      videos.forEach((video, index) => {
        video.addEventListener("loadedmetadata", () => {
          this.notifyVideoLoaded(video, index);
        });

        video.addEventListener("play", () => {
          this.notifyVideoPlay(video, index);
        });

        video.addEventListener("pause", () => {
          this.notifyVideoPause(video, index);
        });
      });

      // Use MutationObserver to detect new video elements
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const newVideos = node.querySelectorAll
                ? node.querySelectorAll("video")
                : [];
              newVideos.forEach((video, index) => {
                this.setupVideoMonitoring(video, index);
              });
            }
          });
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    }

    setupVideoMonitoring(video, index) {
      video.addEventListener("loadedmetadata", () => {
        this.notifyVideoLoaded(video, index);
      });
    }

    setupCanvasCapture() {
      // Monitor canvas elements for potential capture
      const canvases = document.querySelectorAll("canvas");
      canvases.forEach((canvas, index) => {
        this.notifyCanvasFound(canvas, index);
      });
    }

    notifyVideoLoaded(video, index) {
      this.sendMessage({
        type: "videoLoaded",
        data: {
          index,
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration,
          src: video.src || video.currentSrc,
        },
      });
    }

    notifyVideoPlay(video, index) {
      this.sendMessage({
        type: "videoPlay",
        data: { index },
      });
    }

    notifyVideoPause(video, index) {
      this.sendMessage({
        type: "videoPause",
        data: { index },
      });
    }

    notifyCanvasFound(canvas, index) {
      this.sendMessage({
        type: "canvasFound",
        data: {
          index,
          width: canvas.width,
          height: canvas.height,
        },
      });
    }

    async captureCanvas(canvasIndex) {
      const canvases = document.querySelectorAll("canvas");
      if (canvasIndex >= canvases.length) {
        throw new Error("Canvas index out of range");
      }

      const canvas = canvases[canvasIndex];
      try {
        const stream = canvas.captureStream(30); // 30 FPS
        return stream;
      } catch (error) {
        throw new Error(`Failed to capture canvas: ${error.message}`);
      }
    }

    sendMessage(data) {
      window.postMessage(
        {
          type: "VIDEO_STREAM_EXTENSION",
          payload: data,
        },
        "*"
      );
    }
  }

  // Initialize injected capture
  const injectedCapture = new InjectedVideoCapture();

  // Expose methods to content script
  window.videoStreamExtension = {
    captureCanvas: (index) => injectedCapture.captureCanvas(index),
    getVideoElements: () => {
      const videos = Array.from(document.querySelectorAll("video"));
      return videos.map((video, index) => ({
        index,
        src: video.src || video.currentSrc,
        width: video.videoWidth,
        height: video.videoHeight,
        duration: video.duration,
        paused: video.paused,
      }));
    },
  };
})();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cross-Origin Iframe Communication - Child</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: calc(100vh - 40px);
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.ready {
            background-color: rgba(40, 167, 69, 0.3);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        button:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .log {
            background-color: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        .info-display {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .info-item {
            margin: 5px 0;
            font-size: 14px;
        }
        h1 {
            margin-top: 0;
            text-align: center;
        }
        h3 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Cross-Origin Iframe - Child Frame</h1>
        <p><strong>Origin:</strong> <span id="iframeOrigin"></span></p>
        
        <div id="connectionStatus" class="status ready">
            ✅ Iframe ready for communication
        </div>

        <div class="controls">
            <button onclick="sendGreeting()">Send Greeting</button>
            <button onclick="sendError()">Send Error</button>
            <button onclick="sendRandomData()">Send Random Data</button>
        </div>

        <div class="info-display">
            <h3>📊 Current Information</h3>
            <div class="info-item"><strong>URL:</strong> <span id="currentUrl"></span></div>
            <div class="info-item"><strong>Viewport:</strong> <span id="viewport"></span></div>
            <div class="info-item"><strong>Parent Origin:</strong> <span id="parentOrigin">Not detected</span></div>
            <div class="info-item"><strong>Messages Received:</strong> <span id="messageCount">0</span></div>
        </div>

        <h3>📋 Message Log</h3>
        <div id="messageLog" class="log"></div>
        
        <button onclick="clearLog()" style="margin-top: 10px;">Clear Log</button>
    </div>

    <script>
        // Security configuration
        const ALLOWED_ORIGINS = ['http://localhost:3001'];
        
        // DOM elements
        const messageLog = document.getElementById('messageLog');
        const messageCountElement = document.getElementById('messageCount');
        const parentOriginElement = document.getElementById('parentOrigin');
        
        // State
        let messageCount = 0;
        let parentOrigin = null;
        let messageCounter = 0;
        
        // Initialize
        document.getElementById('iframeOrigin').textContent = window.location.origin;
        document.getElementById('currentUrl').textContent = window.location.href;
        updateViewport();
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            
            // Also log to console for debugging
            console.log(`Iframe: ${message}`);
        }
        
        function updateViewport() {
            const viewport = `${window.innerWidth}x${window.innerHeight}`;
            document.getElementById('viewport').textContent = viewport;
        }
        
        function updateMessageCount() {
            messageCount++;
            messageCountElement.textContent = messageCount;
        }
        
        // Listen for messages from parent
        window.addEventListener('message', function(event) {
            log(`Received message from: ${event.origin}`, 'receive');
            
            // Security check: validate origin
            if (!ALLOWED_ORIGINS.includes(event.origin)) {
                log(`❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`, 'security');
                return;
            }
            
            // Store parent origin for responses
            if (!parentOrigin) {
                parentOrigin = event.origin;
                parentOriginElement.textContent = parentOrigin;
                log(`✅ Parent origin detected: ${parentOrigin}`, 'info');
            }
            
            updateMessageCount();
            
            // Validate message structure
            if (!event.data || typeof event.data !== 'object') {
                log(`❌ SECURITY: Invalid message format received`, 'security');
                return;
            }
            
            const { type, payload, messageId } = event.data;
            
            log(`Message type: ${type}, ID: ${messageId}`, 'receive');
            log(`Payload: ${JSON.stringify(payload)}`, 'receive');
            
            // Handle different message types
            switch (type) {
                case 'echo_request':
                    handleEchoRequest(payload, messageId, event.origin);
                    break;
                    
                case 'data_update':
                    handleDataUpdate(payload, messageId, event.origin);
                    break;
                    
                case 'info_request':
                    handleInfoRequest(messageId, event.origin);
                    break;
                    
                default:
                    log(`⚠️ Unknown message type: ${type}`, 'warning');
                    sendError(`Unknown message type: ${type}`, event.origin);
            }
        });
        
        function handleEchoRequest(payload, messageId, origin) {
            const { message } = payload;
            log(`🔄 Processing echo request: "${message}"`, 'process');
            
            // Simulate some processing time
            setTimeout(() => {
                const response = {
                    type: 'echo_response',
                    payload: { 
                        message: message,
                        processedAt: new Date().toISOString(),
                        originalMessageId: messageId
                    },
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                try {
                    window.parent.postMessage(response, origin);
                    log(`📤 Sent echo response for message ID: ${messageId}`, 'send');
                } catch (error) {
                    log(`❌ Failed to send echo response: ${error.message}`, 'error');
                }
            }, 100);
        }
        
        function handleDataUpdate(payload, messageId, origin) {
            log(`📊 Processing data update...`, 'process');
            log(`User: ${payload.user?.name} (ID: ${payload.user?.id})`, 'process');
            log(`Preferences: ${JSON.stringify(payload.user?.preferences)}`, 'process');
            log(`Metadata: ${JSON.stringify(payload.metadata)}`, 'process');
            
            // Acknowledge receipt
            const response = {
                type: 'data_received',
                payload: { 
                    status: 'success',
                    recordsProcessed: 1,
                    originalMessageId: messageId
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                window.parent.postMessage(response, origin);
                log(`📤 Sent data acknowledgment for message ID: ${messageId}`, 'send');
            } catch (error) {
                log(`❌ Failed to send acknowledgment: ${error.message}`, 'error');
            }
        }
        
        function handleInfoRequest(messageId, origin) {
            log(`📋 Processing info request...`, 'process');
            
            const info = {
                type: 'iframe_info',
                payload: {
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    timestamp: new Date().toISOString(),
                    originalMessageId: messageId
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                window.parent.postMessage(info, origin);
                log(`📤 Sent iframe info for message ID: ${messageId}`, 'send');
            } catch (error) {
                log(`❌ Failed to send info: ${error.message}`, 'error');
            }
        }
        
        function sendGreeting() {
            if (!parentOrigin) {
                log('❌ Cannot send greeting: parent origin not detected', 'error');
                return;
            }
            
            const greeting = {
                type: 'greeting',
                payload: { 
                    message: 'Hello from iframe!',
                    timestamp: new Date().toISOString()
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                window.parent.postMessage(greeting, parentOrigin);
                log(`📤 Sent greeting to parent`, 'send');
            } catch (error) {
                log(`❌ Failed to send greeting: ${error.message}`, 'error');
            }
        }
        
        function sendError(errorMessage = 'Test error from iframe', origin = null) {
            const targetOrigin = origin || parentOrigin;
            
            if (!targetOrigin) {
                log('❌ Cannot send error: no target origin', 'error');
                return;
            }
            
            const error = {
                type: 'error',
                payload: { 
                    message: errorMessage,
                    timestamp: new Date().toISOString()
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                window.parent.postMessage(error, targetOrigin);
                log(`📤 Sent error message: "${errorMessage}"`, 'send');
            } catch (error) {
                log(`❌ Failed to send error: ${error.message}`, 'error');
            }
        }
        
        function sendRandomData() {
            if (!parentOrigin) {
                log('❌ Cannot send data: parent origin not detected', 'error');
                return;
            }
            
            const randomData = {
                type: 'random_data',
                payload: {
                    randomNumber: Math.floor(Math.random() * 1000),
                    randomString: Math.random().toString(36).substring(7),
                    timestamp: new Date().toISOString(),
                    colors: ['red', 'green', 'blue'][Math.floor(Math.random() * 3)]
                },
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            try {
                window.parent.postMessage(randomData, parentOrigin);
                log(`📤 Sent random data`, 'send');
            } catch (error) {
                log(`❌ Failed to send random data: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Handle window resize
        window.addEventListener('resize', updateViewport);
        
        // Send ready signal to parent when loaded
        window.addEventListener('load', function() {
            log('🚀 Iframe loaded, sending ready signal...', 'info');
            
            // Wait a bit to ensure parent is ready to receive messages
            setTimeout(() => {
                const readyMessage = {
                    type: 'iframe_ready',
                    payload: {
                        origin: window.location.origin,
                        url: window.location.href,
                        timestamp: new Date().toISOString()
                    },
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                try {
                    // Send to all allowed origins since we don't know which one is the parent yet
                    ALLOWED_ORIGINS.forEach(origin => {
                        window.parent.postMessage(readyMessage, origin);
                    });
                    log('📤 Sent ready signal to parent', 'send');
                } catch (error) {
                    log(`❌ Failed to send ready signal: ${error.message}`, 'error');
                }
            }, 100);
        });
        
        // Initial log entry
        log('🎯 Iframe initialized', 'info');
        log(`Iframe origin: ${window.location.origin}`, 'info');
        log(`Allowed parent origins: ${ALLOWED_ORIGINS.join(', ')}`, 'info');
    </script>
</body>
</html>

// Background service worker for Tab Screen Share Extension
class TabScreenShareExtension {
  constructor() {
    this.activeCaptures = new Map();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      console.log("Tab Screen Share Extension installed");
    });

    // Handle messages from popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle tab removal - cleanup any active captures
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.stopCapture(tabId);
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case "getActiveTab":
          const activeTab = await this.getActiveTab();
          sendResponse({ success: true, data: activeTab });
          break;

        case "startCapture":
          // Ensure we're working with the active tab
          const currentTab = await this.getActiveTab();
          if (!currentTab) {
            throw new Error("No active tab found");
          }
          console.log(
            "Starting capture for tab:",
            currentTab.id,
            currentTab.url
          );
          const captureResult = await this.startTabCapture(currentTab.id);
          sendResponse({ success: true, data: captureResult });
          break;

        case "stopCapture":
          await this.stopCapture(message.tabId);
          sendResponse({ success: true });
          break;

        case "getCaptureStatus":
          const status = this.getCaptureStatus(message.tabId);
          sendResponse({ success: true, data: status });
          break;

        default:
          sendResponse({ success: false, error: "Unknown action" });
      }
    } catch (error) {
      console.error("Background script error:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getActiveTab() {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });
    return tab;
  }

  async startTabCapture(tabId) {
    try {
      // Check if already capturing this tab
      if (this.activeCaptures.has(tabId)) {
        throw new Error("Tab is already being captured");
      }

      console.log("Attempting to capture tab:", tabId);

      // Check if tabCapture API is available
      if (!chrome.tabCapture || !chrome.tabCapture.capture) {
        throw new Error("tabCapture API is not available");
      }

      // Start tab capture with audio and video using the correct API
      const stream = await new Promise((resolve, reject) => {
        chrome.tabCapture.capture(
          {
            audio: true,
            video: true,
          },
          (stream) => {
            console.log("tabCapture.capture callback called");
            console.log("chrome.runtime.lastError:", chrome.runtime.lastError);
            console.log("stream:", stream);

            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else if (!stream) {
              reject(new Error("Failed to capture tab - no stream returned"));
            } else {
              resolve(stream);
            }
          }
        );
      });

      // Store capture info
      const captureInfo = {
        tabId,
        stream,
        startTime: Date.now(),
        audioTracks: stream.getAudioTracks().length,
        videoTracks: stream.getVideoTracks().length,
      };

      this.activeCaptures.set(tabId, captureInfo);

      // Set up stream event listeners
      this.setupStreamListeners(stream, tabId);

      console.log(`Started tab capture for tab ${tabId}`);
      return {
        tabId,
        audioTracks: captureInfo.audioTracks,
        videoTracks: captureInfo.videoTracks,
        startTime: captureInfo.startTime,
      };
    } catch (error) {
      console.error(`Failed to start tab capture for ${tabId}:`, error);
      throw new Error(`Tab capture failed: ${error.message}`);
    }
  }

  setupStreamListeners(stream, tabId) {
    // Listen for track events
    stream.getTracks().forEach((track) => {
      track.addEventListener("ended", () => {
        console.log(`Track ended for tab ${tabId}:`, track.kind);
        this.handleTrackEnded(tabId, track);
      });

      track.addEventListener("mute", () => {
        console.log(`Track muted for tab ${tabId}:`, track.kind);
      });

      track.addEventListener("unmute", () => {
        console.log(`Track unmuted for tab ${tabId}:`, track.kind);
      });
    });
  }

  handleTrackEnded(tabId, track) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (captureInfo) {
      // Check if all tracks have ended
      const activeTracks = captureInfo.stream
        .getTracks()
        .filter((t) => t.readyState === "live");
      if (activeTracks.length === 0) {
        console.log(`All tracks ended for tab ${tabId}, stopping capture`);
        this.stopCapture(tabId);
      }
    }
  }

  async stopCapture(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      return false;
    }

    try {
      // Stop all tracks
      captureInfo.stream.getTracks().forEach((track) => {
        track.stop();
      });

      // Remove from active captures
      this.activeCaptures.delete(tabId);

      console.log(`Stopped tab capture for tab ${tabId}`);
      return true;
    } catch (error) {
      console.error(`Error stopping capture for tab ${tabId}:`, error);
      return false;
    }
  }

  getCaptureStatus(tabId) {
    const captureInfo = this.activeCaptures.get(tabId);
    if (!captureInfo) {
      return { isCapturing: false };
    }

    const stream = captureInfo.stream;
    const activeTracks = stream
      .getTracks()
      .filter((t) => t.readyState === "live");

    return {
      isCapturing: true,
      tabId: captureInfo.tabId,
      startTime: captureInfo.startTime,
      duration: Date.now() - captureInfo.startTime,
      audioTracks: stream.getAudioTracks().length,
      videoTracks: stream.getVideoTracks().length,
      activeTracks: activeTracks.length,
      audioEnabled: stream
        .getAudioTracks()
        .some((t) => t.enabled && t.readyState === "live"),
      videoEnabled: stream
        .getVideoTracks()
        .some((t) => t.enabled && t.readyState === "live"),
    };
  }

  getAllActiveCaptures() {
    return Array.from(this.activeCaptures.keys()).map((tabId) =>
      this.getCaptureStatus(tabId)
    );
  }

  cleanup() {
    // Stop all active captures
    const tabIds = Array.from(this.activeCaptures.keys());
    tabIds.forEach((tabId) => {
      this.stopCapture(tabId);
    });
  }
}

// Initialize the extension
const tabScreenShareExtension = new TabScreenShareExtension();

// Cleanup on extension unload
chrome.runtime.onSuspend.addListener(() => {
  tabScreenShareExtension.cleanup();
});

# Extension Icons

This directory should contain the extension icons in the following sizes:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows computers often use this size)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store and installation)

## Creating Icons

You can create simple placeholder icons or use a proper icon design tool. For testing purposes, you can:

1. Create simple colored squares with the extension name
2. Use online icon generators
3. Design custom icons that represent video streaming

## Icon Requirements

- PNG format
- Transparent background recommended
- Clear and recognizable at small sizes
- Consistent design across all sizes

For now, you can create simple placeholder icons or the extension will work without them (Chrome will use default icons).

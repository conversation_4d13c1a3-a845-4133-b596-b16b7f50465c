<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complex Multi-Tab - Tab 2 (Window Manager)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .role-badge {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #ff6b6b;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin: 15px 0;
            align-items: center;
        }
        .control-row {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        input[type="text"], input[type="url"] {
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            flex: 1;
            min-width: 200px;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        .btn-secondary {
            background: #ffeaa7;
            color: #333;
        }
        .btn-danger {
            background: #d63031;
            color: white;
        }
        .btn-success {
            background: #00b894;
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .log {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .child-windows {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .window-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 10px;
        }
        .window-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .window-info {
            flex: 1;
        }
        .window-controls {
            display: flex;
            gap: 5px;
        }
        .window-controls button {
            padding: 6px 12px;
            font-size: 12px;
        }
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🪟 Complex Multi-Tab Communication</h1>
            <div class="role-badge">Tab 2 - Window Manager</div>
            <p>This tab opens child windows that can be controlled by other tabs</p>
            <p><strong>Tab ID:</strong> <span id="tabId"></span></p>
        </div>

        <div class="instructions">
            <h4>📋 Instructions</h4>
            <ol>
                <li>Open child windows using the controls below</li>
                <li>Open <strong>Tab 1</strong> in another browser tab</li>
                <li>Use Tab 1 to send messages to the child windows opened here</li>
                <li>Observe how Tab 1 communicates with children opened by Tab 2</li>
            </ol>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h4>🪟 Child Windows</h4>
                <div id="childWindowCount">0 windows open</div>
            </div>
            <div class="status-card">
                <h4>📡 Network Status</h4>
                <div id="networkStatus">Initializing...</div>
            </div>
            <div class="status-card">
                <h4>📊 Messages</h4>
                <div id="messageStats">Sent: 0, Received: 0</div>
            </div>
        </div>

        <div class="child-windows">
            <h4>🔗 Managed Child Windows</h4>
            <div id="childWindowsList" class="window-list">
                <div style="color: #ccc; font-style: italic;">No child windows open</div>
            </div>
        </div>

        <div class="container">
            <h3>🪟 Window Management</h3>
            <div class="controls">
                <input type="url" id="childUrl" placeholder="Child window URL..." value="/multi-tab-complex/child.html">
                <button class="btn-primary" onclick="openChildWindow()">Open Child Window</button>
            </div>
            
            <div class="control-row">
                <button class="btn-secondary" onclick="openMultipleWindows()">Open 3 Windows</button>
                <button class="btn-success" onclick="broadcastToChildren()">Broadcast to Children</button>
                <button class="btn-danger" onclick="closeAllWindows()">Close All</button>
            </div>
        </div>

        <div class="container">
            <h3>📤 Network Communication</h3>
            <div class="control-row">
                <button class="btn-secondary" onclick="announceChildWindows()">Announce Children</button>
                <button class="btn-secondary" onclick="sendNetworkInfo()">Send Network Info</button>
                <button class="btn-success" onclick="respondToPings()">Enable Ping Response</button>
            </div>
        </div>

        <div class="container">
            <h3>📋 Communication Log</h3>
            <div id="messageLog" class="log"></div>
            <button onclick="clearLog()" style="margin-top: 10px; padding: 8px 16px; background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 4px;">Clear Log</button>
        </div>
    </div>

    <script>
        // Tab identification
        const TAB_ID = 'tab2_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const TAB_NAME = 'Tab 2 (Window Manager)';
        const TAB_ROLE = 'window_manager';
        
        // Communication channels
        let broadcastChannel;
        let childWindows = new Map();
        let messageCounter = 0;
        let sentMessages = 0;
        let receivedMessages = 0;
        let pingResponseEnabled = false;
        
        // DOM elements
        const messageLog = document.getElementById('messageLog');
        const childWindowsList = document.getElementById('childWindowsList');
        const childUrl = document.getElementById('childUrl');
        
        // Initialize
        document.getElementById('tabId').textContent = TAB_ID;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            console.log(`Tab2: ${message}`);
        }
        
        function updateStats() {
            document.getElementById('childWindowCount').textContent = `${childWindows.size} windows open`;
            document.getElementById('messageStats').textContent = `Sent: ${sentMessages}, Received: ${receivedMessages}`;
        }
        
        function updateChildWindowsList() {
            if (childWindows.size === 0) {
                childWindowsList.innerHTML = '<div style="color: #ccc; font-style: italic;">No child windows open</div>';
            } else {
                childWindowsList.innerHTML = Array.from(childWindows.entries())
                    .map(([id, info]) => `
                        <div class="window-item">
                            <div class="window-info">
                                <strong>${info.name}</strong><br>
                                <small>ID: ${id}</small><br>
                                <small>Status: ${info.closed ? 'Closed' : 'Open'}</small>
                            </div>
                            <div class="window-controls">
                                <button class="btn-secondary" onclick="sendToChild('${id}')">Message</button>
                                <button class="btn-danger" onclick="closeChildWindow('${id}')">Close</button>
                            </div>
                        </div>
                    `).join('');
            }
            updateStats();
        }
        
        // Initialize BroadcastChannel
        function initBroadcastChannel() {
            try {
                broadcastChannel = new BroadcastChannel('multi-tab-complex');
                broadcastChannel.addEventListener('message', handleBroadcastMessage);
                document.getElementById('networkStatus').textContent = 'Connected to network';
                log('🔗 Connected to network via BroadcastChannel', 'success');
            } catch (error) {
                document.getElementById('networkStatus').textContent = 'Network connection failed';
                log(`❌ BroadcastChannel error: ${error.message}`, 'error');
            }
        }
        
        // Handle messages from network
        function handleBroadcastMessage(event) {
            const data = event.data;
            log(`📡 Network message: ${data.type}`, 'receive');
            processNetworkMessage(data);
        }
        
        // Process network messages
        function processNetworkMessage(data) {
            const { type, payload, senderId, senderName, senderRole, timestamp } = data;
            receivedMessages++;
            updateStats();
            
            switch (type) {
                case 'node_announcement':
                    log(`📢 Node joined network: ${senderName} (${senderRole})`, 'network');
                    break;
                    
                case 'message_to_children':
                    log(`📤 Relaying message to children from ${senderName}`, 'relay');
                    relayMessageToChildren(data);
                    break;
                    
                case 'network_info_request':
                    log(`📋 Network info requested by ${senderName}`, 'request');
                    sendNetworkInfo();
                    break;
                    
                case 'ping_request':
                    if (pingResponseEnabled) {
                        log(`🏓 Ping from ${senderName}`, 'ping');
                        sendPingResponse(data);
                    }
                    break;
                    
                case 'child_list_request':
                    log(`📋 Child list requested by ${senderName}`, 'request');
                    sendChildList();
                    break;
                    
                case 'emergency_signal':
                    log(`🚨 Emergency signal from ${senderName}: ${payload.message}`, 'emergency');
                    handleEmergencySignal(data);
                    break;
                    
                case 'data_packet':
                    log(`📦 Data packet from ${senderName}`, 'data');
                    relayDataPacketToChildren(data);
                    break;
                    
                case 'node_disconnect':
                    log(`👋 ${senderName} disconnected from network`, 'network');
                    break;
                    
                default:
                    log(`⚠️ Unknown network message: ${type}`, 'warning');
            }
        }
        
        // Listen for messages from child windows
        window.addEventListener('message', function(event) {
            // Validate origin
            if (event.origin !== window.location.origin) {
                log(`❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`, 'security');
                return;
            }
            
            // Validate message structure
            if (!event.data || typeof event.data !== 'object') {
                log(`❌ SECURITY: Invalid message format received`, 'security');
                return;
            }
            
            const { type, childId } = event.data;
            log(`📥 Message from child ${childId}: ${type}`, 'receive');
            
            // Handle child window messages
            switch (type) {
                case 'child_ready':
                    log(`✅ Child window ready: ${childId}`, 'success');
                    announceChildWindow(childId);
                    break;
                    
                case 'child_closing':
                    log(`👋 Child window closing: ${childId}`, 'info');
                    announceChildWindowClosed(childId);
                    break;
                    
                default:
                    // Relay other messages to network if needed
                    relayChildMessageToNetwork(event.data);
            }
        });
        
        function openChildWindow() {
            const url = childUrl.value || '/multi-tab-complex/child.html';
            const width = 500;
            const height = 400;
            const left = 200 + (childWindows.size * 50);
            const top = 200 + (childWindows.size * 50);
            
            const features = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`;
            
            try {
                const childWindow = window.open(url, '_blank', features);
                
                if (childWindow) {
                    const childId = 'child_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    
                    childWindows.set(childId, {
                        window: childWindow,
                        name: `Child ${childWindows.size + 1}`,
                        url: url,
                        opened: Date.now(),
                        closed: false
                    });
                    
                    log(`🪟 Opened child window: ${childId}`, 'success');
                    updateChildWindowsList();
                    
                    // Monitor if window gets closed
                    const checkClosed = setInterval(() => {
                        if (childWindow.closed) {
                            clearInterval(checkClosed);
                            const info = childWindows.get(childId);
                            if (info) {
                                info.closed = true;
                                log(`🪟 Child window closed: ${childId}`, 'info');
                                updateChildWindowsList();
                                announceChildWindowClosed(childId);
                            }
                        }
                    }, 1000);
                    
                } else {
                    log('❌ Failed to open child window (popup blocked?)', 'error');
                }
            } catch (error) {
                log(`❌ Error opening child window: ${error.message}`, 'error');
            }
        }
        
        function openMultipleWindows() {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    openChildWindow();
                }, i * 300);
            }
        }
        
        function closeChildWindow(childId) {
            const childInfo = childWindows.get(childId);
            if (childInfo && !childInfo.window.closed) {
                childInfo.window.close();
                log(`🪟 Closed child window: ${childId}`, 'info');
            }
        }
        
        function closeAllWindows() {
            for (const [childId, info] of childWindows.entries()) {
                if (!info.window.closed) {
                    info.window.close();
                }
            }
            log('🪟 Closed all child windows', 'info');
        }
        
        function sendToChild(childId) {
            const message = prompt('Enter message for this child window:');
            if (message) {
                const childInfo = childWindows.get(childId);
                if (childInfo && !childInfo.window.closed) {
                    const messageData = {
                        type: 'direct_message',
                        payload: { message: message },
                        parentId: TAB_ID,
                        messageId: ++messageCounter,
                        timestamp: Date.now()
                    };
                    
                    try {
                        childInfo.window.postMessage(messageData, window.location.origin);
                        log(`📤 Sent message to ${childId}: "${message}"`, 'send');
                        sentMessages++;
                        updateStats();
                    } catch (error) {
                        log(`❌ Failed to send to ${childId}: ${error.message}`, 'error');
                    }
                }
            }
        }
        
        function broadcastToChildren() {
            const message = prompt('Enter message to broadcast to all children:');
            if (message) {
                const messageData = {
                    type: 'broadcast_message',
                    payload: { message: message },
                    parentId: TAB_ID,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                let successCount = 0;
                for (const [childId, info] of childWindows.entries()) {
                    if (!info.window.closed) {
                        try {
                            info.window.postMessage(messageData, window.location.origin);
                            successCount++;
                        } catch (error) {
                            log(`❌ Failed to send to ${childId}: ${error.message}`, 'error');
                        }
                    }
                }
                
                if (successCount > 0) {
                    log(`📤 Broadcast message to ${successCount} children: "${message}"`, 'send');
                    sentMessages++;
                    updateStats();
                }
            }
        }
        
        function announceChildWindow(childId) {
            const childInfo = childWindows.get(childId);
            if (childInfo) {
                const announcementData = {
                    type: 'child_window_opened',
                    payload: {
                        childId: childId,
                        childName: childInfo.name,
                        childUrl: childInfo.url,
                        openedAt: childInfo.opened
                    },
                    senderId: TAB_ID,
                    senderName: TAB_NAME,
                    senderRole: TAB_ROLE,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                sendBroadcast(announcementData);
                log(`📢 Announced child window to network: ${childId}`, 'send');
            }
        }
        
        function announceChildWindowClosed(childId) {
            const closedData = {
                type: 'child_window_closed',
                payload: { childId: childId },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(closedData);
            log(`📢 Announced child window closed: ${childId}`, 'send');
        }
        
        function announceChildWindows() {
            for (const [childId, info] of childWindows.entries()) {
                if (!info.closed) {
                    announceChildWindow(childId);
                }
            }
        }
        
        function sendNetworkInfo() {
            const networkInfoData = {
                type: 'network_info_response',
                payload: {
                    childCount: childWindows.size,
                    activeConnections: Array.from(childWindows.values()).filter(info => !info.closed).length,
                    role: TAB_ROLE,
                    capabilities: ['window_management', 'child_communication', 'message_relay']
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(networkInfoData);
            log('📊 Sent network information', 'send');
        }
        
        function sendPingResponse(pingData) {
            const responseData = {
                type: 'ping_response',
                payload: {
                    originalTimestamp: pingData.payload.originalTimestamp,
                    responseTimestamp: Date.now()
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(responseData);
            log('🏓 Sent ping response', 'send');
        }
        
        function sendChildList() {
            const childListData = {
                type: 'child_list_response',
                payload: {
                    children: Array.from(childWindows.entries()).map(([id, info]) => ({
                        id: id,
                        name: info.name,
                        url: info.url,
                        opened: info.opened,
                        closed: info.closed
                    }))
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(childListData);
            log('📋 Sent child window list', 'send');
        }
        
        function handleEmergencySignal(emergencyData) {
            const responseData = {
                type: 'emergency_response',
                payload: {
                    status: 'acknowledged',
                    childWindowsAffected: childWindows.size,
                    action: 'standing_by'
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(responseData);
            log('🚨 Acknowledged emergency signal', 'send');
        }
        
        function relayMessageToChildren(messageData) {
            const relayData = {
                type: 'relayed_message',
                payload: {
                    originalMessage: messageData.payload.message,
                    originalSender: messageData.senderName,
                    relayedBy: TAB_NAME
                },
                parentId: TAB_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            let relayCount = 0;
            for (const [childId, info] of childWindows.entries()) {
                if (!info.window.closed) {
                    try {
                        info.window.postMessage(relayData, window.location.origin);
                        relayCount++;
                    } catch (error) {
                        log(`❌ Failed to relay to ${childId}: ${error.message}`, 'error');
                    }
                }
            }
            
            if (relayCount > 0) {
                log(`🔄 Relayed message to ${relayCount} children`, 'relay');
                sentMessages++;
                updateStats();
            }
        }
        
        function relayDataPacketToChildren(dataPacketData) {
            const relayData = {
                type: 'data_packet_relay',
                payload: {
                    originalData: dataPacketData.payload.data,
                    originalSender: dataPacketData.senderName,
                    relayedBy: TAB_NAME
                },
                parentId: TAB_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            let relayCount = 0;
            for (const [childId, info] of childWindows.entries()) {
                if (!info.window.closed) {
                    try {
                        info.window.postMessage(relayData, window.location.origin);
                        relayCount++;
                    } catch (error) {
                        log(`❌ Failed to relay data to ${childId}: ${error.message}`, 'error');
                    }
                }
            }
            
            if (relayCount > 0) {
                log(`📦 Relayed data packet to ${relayCount} children`, 'relay');
                sentMessages++;
                updateStats();
            }
        }
        
        function relayChildMessageToNetwork(childMessage) {
            const relayData = {
                type: 'child_message',
                payload: {
                    childId: childMessage.childId,
                    message: childMessage.payload?.message || 'Child message',
                    relayedBy: TAB_NAME
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(relayData);
            log(`🔄 Relayed child message to network`, 'relay');
        }
        
        function respondToPings() {
            pingResponseEnabled = !pingResponseEnabled;
            const button = event.target;
            if (pingResponseEnabled) {
                button.textContent = 'Disable Ping Response';
                button.className = 'btn-danger';
                log('🏓 Ping response enabled', 'info');
            } else {
                button.textContent = 'Enable Ping Response';
                button.className = 'btn-success';
                log('🏓 Ping response disabled', 'info');
            }
        }
        
        function sendBroadcast(data) {
            if (broadcastChannel) {
                try {
                    broadcastChannel.postMessage(data);
                    sentMessages++;
                    updateStats();
                    return true;
                } catch (error) {
                    log(`❌ Failed to send broadcast: ${error.message}`, 'error');
                    return false;
                }
            } else {
                log('❌ BroadcastChannel not available', 'error');
                return false;
            }
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Send node announcement when loaded
        function announceNode() {
            const announcementData = {
                type: 'node_announcement',
                payload: {
                    role: TAB_ROLE,
                    capabilities: ['window_management', 'child_communication', 'message_relay'],
                    version: '1.0.0'
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendBroadcast(announcementData);
            log('📢 Announced node to network', 'send');
        }
        
        // Send disconnect signal when tab is closing
        window.addEventListener('beforeunload', function() {
            // Close all child windows
            for (const [childId, info] of childWindows.entries()) {
                if (!info.window.closed) {
                    info.window.close();
                }
            }
            
            // Send disconnect notification
            const disconnectData = {
                type: 'node_disconnect',
                payload: { reason: 'Tab closing' },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                senderRole: TAB_ROLE,
                timestamp: Date.now()
            };
            
            if (broadcastChannel) {
                try {
                    broadcastChannel.postMessage(disconnectData);
                } catch (error) {
                    // Ignore errors during shutdown
                }
            }
        });
        
        // Periodically check for closed windows
        setInterval(() => {
            let hasChanges = false;
            for (const [childId, info] of childWindows.entries()) {
                if (!info.closed && info.window.closed) {
                    info.closed = true;
                    hasChanges = true;
                    announceChildWindowClosed(childId);
                }
            }
            if (hasChanges) {
                updateChildWindowsList();
            }
        }, 2000);
        
        // Initialize everything
        initBroadcastChannel();
        
        // Announce this node after a short delay
        setTimeout(announceNode, 1000);
        
        log('🚀 Tab 2 (Window Manager) initialized', 'success');
        log('Ready to open and manage child windows', 'info');
    </script>
</body>
</html>

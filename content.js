// Content script for Video Stream Extension
class ContentScriptManager {
  constructor() {
    this.isInjected = false;
    this.setupMessageListener();
    this.injectHelperScript();
  }

  setupMessageListener() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Listen for messages from injected script
    window.addEventListener('message', (event) => {
      if (event.source !== window || !event.data.type) return;
      
      if (event.data.type === 'VIDEO_STREAM_EXTENSION') {
        this.handleInjectedMessage(event.data);
      }
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'getPageInfo':
          const pageInfo = this.getPageInfo();
          sendResponse({ success: true, data: pageInfo });
          break;

        case 'injectCapture':
          await this.injectCaptureScript();
          sendResponse({ success: true });
          break;

        case 'getVideoElements':
          const videoElements = this.getVideoElements();
          sendResponse({ success: true, data: videoElements });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  handleInjectedMessage(data) {
    // Forward messages from injected script to background
    chrome.runtime.sendMessage({
      action: 'injectedMessage',
      data: data.payload
    });
  }

  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      hasVideo: document.querySelectorAll('video').length > 0,
      hasCanvas: document.querySelectorAll('canvas').length > 0,
      timestamp: Date.now()
    };
  }

  getVideoElements() {
    const videos = Array.from(document.querySelectorAll('video'));
    return videos.map((video, index) => ({
      index,
      src: video.src || video.currentSrc,
      width: video.videoWidth,
      height: video.videoHeight,
      duration: video.duration,
      paused: video.paused,
      muted: video.muted,
      volume: video.volume
    }));
  }

  async injectCaptureScript() {
    if (this.isInjected) return;

    try {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('injected.js');
      script.onload = () => {
        script.remove();
        this.isInjected = true;
      };
      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      throw new Error(`Failed to inject script: ${error.message}`);
    }
  }

  injectHelperScript() {
    // Inject helper script immediately
    this.injectCaptureScript().catch(console.error);
  }
}

// Initialize content script
const contentManager = new ContentScriptManager();

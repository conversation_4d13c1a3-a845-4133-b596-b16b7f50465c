# Inter-Tab Communication Example

This example demonstrates multiple methods for communication between browser tabs using various web APIs including BroadcastChannel, LocalStorage events, and SharedWorker.

## 🎯 What This Example Demonstrates

- **Multiple communication methods** (BroadcastChannel, LocalStorage, SharedWorker)
- **Real-time tab discovery** and connection tracking
- **Method switching** to compare different approaches
- **Advanced messaging patterns** (bulk messages, typing indicators, emergency broadcasts)
- **Automatic cleanup** of disconnected tabs
- **Comprehensive logging** and debugging

## 🏗️ Architecture

```
Tab 1 (Blue Theme)                    Tab 2 (Pink Theme)
├── BroadcastChannel                  ├── BroadcastChannel
├── LocalStorage Events              ├── LocalStorage Events  
├── SharedWorker                     ├── SharedWorker
├── Basic messaging                  ├── Basic messaging
└── Tab discovery                    └── Advanced features
                                         ├── Bulk messaging
                                         ├── Formatted data
                                         ├── Typing simulation
                                         └── Emergency broadcasts
```

## 🚀 How to Test

### 1. Start the Server
```bash
# From the project root
npm run start-servers
```

### 2. Open Multiple Tabs
1. **Tab 1**: `http://localhost:3001/inter-tab/tab1.html`
2. **Tab 2**: `http://localhost:3001/inter-tab/tab2.html`
3. **Additional tabs**: Open more instances of either tab

### 3. Test Communication
1. **Basic messaging**: Type messages and send between tabs
2. **Method switching**: Try different communication methods (BroadcastChannel, LocalStorage, SharedWorker)
3. **Tab discovery**: Click "Request Tab List" to discover other tabs
4. **Heartbeats**: Send heartbeats to maintain connection
5. **Special features** (Tab 2 only):
   - Bulk messages
   - Formatted data
   - Typing simulation
   - Emergency broadcasts

## 📡 Communication Methods

### 1. BroadcastChannel API
```javascript
const channel = new BroadcastChannel('inter-tab-demo');
channel.postMessage(data);
channel.addEventListener('message', handleMessage);
```

**Pros:**
- Real-time communication
- Simple API
- Automatic cleanup when tab closes

**Cons:**
- Not supported in all browsers
- Same-origin only

### 2. LocalStorage Events
```javascript
localStorage.setItem('inter-tab-message', JSON.stringify(data));
window.addEventListener('storage', handleStorageEvent);
```

**Pros:**
- Wide browser support
- Persistent until cleared

**Cons:**
- Not real-time (polling needed)
- Storage limitations
- Manual cleanup required

### 3. SharedWorker
```javascript
const worker = new SharedWorker(workerScript);
worker.port.postMessage(data);
worker.port.addEventListener('message', handleMessage);
```

**Pros:**
- Shared state between tabs
- Can handle complex logic
- Real-time communication

**Cons:**
- Limited browser support
- More complex setup
- Debugging challenges

## 📨 Message Types

### Basic Messages
- `chat_message` - Simple text messages
- `heartbeat` - Keep-alive signals
- `tab_list_request` - Request list of connected tabs
- `tab_info` - Tab information response
- `tab_disconnect` - Tab closing notification

### Advanced Messages (Tab 2)
- `bulk_message` - Sequential bulk messages
- `formatted_data` - Structured data objects
- `typing_indicator` - Typing status
- `emergency` - High-priority broadcasts

## 🔒 Security Considerations

### Origin Validation
All tabs operate on the same origin, but in a real application:
```javascript
// Validate message origin
if (event.origin !== 'https://yourdomain.com') {
    return; // Reject message
}
```

### Message Validation
```javascript
// Validate message structure
if (!data || typeof data !== 'object' || !data.type) {
    return; // Reject malformed message
}
```

### Data Sanitization
```javascript
// Sanitize user input before sending
const sanitizedMessage = message.replace(/<script>/gi, '');
```

## 🐛 Debugging Features

### Visual Logging
- Real-time message logs in each tab
- Color-coded message types
- Timestamp tracking
- Method identification

### Console Logging
All messages are also logged to browser console:
```javascript
console.log(`Tab1: Sent message via broadcast`);
```

### Connection Tracking
- Live display of connected tabs
- Last seen timestamps
- Automatic cleanup of stale connections

## ⚠️ Browser Compatibility

| Feature | Chrome | Firefox | Safari | Edge |
|---------|--------|---------|--------|------|
| BroadcastChannel | ✅ 54+ | ✅ 38+ | ✅ 15.4+ | ✅ 79+ |
| LocalStorage Events | ✅ All | ✅ All | ✅ All | ✅ All |
| SharedWorker | ✅ All | ✅ All | ❌ No | ✅ All |

## 🔧 Customization

### Adding New Message Types
1. Add handler in `processMessage()`:
```javascript
case 'new_message_type':
    log(`New message: ${payload.data}`, 'custom');
    break;
```

2. Add sender function:
```javascript
function sendNewMessage() {
    const data = {
        type: 'new_message_type',
        payload: { data: 'your data' },
        senderId: TAB_ID,
        senderName: TAB_NAME,
        messageId: ++messageCounter,
        timestamp: Date.now()
    };
    sendViaMethod(data, currentMethod);
}
```

### Changing Communication Channel
Update the channel name in both tabs:
```javascript
const channel = new BroadcastChannel('your-channel-name');
```

### Adding Persistence
Store important messages in localStorage:
```javascript
const persistentMessages = JSON.parse(localStorage.getItem('messages') || '[]');
persistentMessages.push(messageData);
localStorage.setItem('messages', JSON.stringify(persistentMessages));
```

## 📚 Learning Points

1. **Choose the right method** based on browser support and requirements
2. **Handle connection lifecycle** (connect, heartbeat, disconnect)
3. **Implement message validation** for security
4. **Use unique identifiers** for tabs and messages
5. **Clean up resources** when tabs close
6. **Provide fallbacks** for unsupported features
7. **Log everything** for debugging purposes

## 🚀 Advanced Patterns

### Request-Response Pattern
```javascript
// Sender
const requestId = generateUniqueId();
sendMessage({ type: 'request', requestId, data });

// Receiver
if (data.type === 'request') {
    sendMessage({ type: 'response', requestId: data.requestId, result });
}
```

### Pub-Sub Pattern
```javascript
// Subscribe to topics
const subscriptions = new Set(['topic1', 'topic2']);

// Publish to topic
sendMessage({ type: 'publish', topic: 'topic1', data });

// Handle published messages
if (data.type === 'publish' && subscriptions.has(data.topic)) {
    handleTopicMessage(data);
}
```

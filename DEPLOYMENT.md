# Deployment Guide

This guide covers deploying the Video Stream Extension and signaling server for production use.

## Chrome Extension Deployment

### Chrome Web Store Submission

#### 1. Prepare Extension Package
```bash
# Create a clean build directory
mkdir extension-build
cp -r *.js *.html *.css *.json icons/ extension-build/

# Remove development files
rm extension-build/TESTING.md
rm extension-build/DEPLOYMENT.md
rm -rf extension-build/signaling-server/

# Create ZIP package
cd extension-build
zip -r video-stream-extension.zip .
```

#### 2. Chrome Web Store Requirements
- **Developer Account**: Register at [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
- **One-time Fee**: $5 registration fee
- **Privacy Policy**: Required for extensions requesting permissions
- **Store Listing**: Screenshots, description, category selection

#### 3. Manifest Validation
Ensure manifest.json meets store requirements:
```json
{
  "manifest_version": 3,
  "name": "Video Stream Extension",
  "version": "1.0.0",
  "description": "Stream video content from browser tabs to external clients",
  "permissions": [
    "activeTab",
    "tabs", 
    "desktopCapture",
    "debugger",
    "storage"
  ]
}
```

#### 4. Store Listing Content
- **Title**: Clear, descriptive name
- **Description**: Detailed functionality explanation
- **Screenshots**: Show extension in action
- **Category**: Developer Tools or Productivity
- **Privacy Policy**: Required for permission usage

### Enterprise Deployment

#### 1. Chrome Enterprise Policy
For enterprise environments, deploy via Chrome Enterprise:

```json
{
  "ExtensionInstallForcelist": [
    "extension-id;https://clients2.google.com/service/update2/crx"
  ],
  "ExtensionSettings": {
    "extension-id": {
      "installation_mode": "force_installed",
      "update_url": "https://clients2.google.com/service/update2/crx"
    }
  }
}
```

#### 2. Self-Hosted Distribution
For internal distribution:
1. Package extension as .crx file
2. Host on internal server
3. Configure Chrome policies to allow installation

## Signaling Server Deployment

### Production Server Setup

#### 1. Server Requirements
- **Node.js**: v16+ recommended
- **Memory**: 512MB minimum, 2GB+ for high load
- **Network**: Stable internet connection
- **SSL Certificate**: Required for HTTPS/WSS

#### 2. Environment Configuration
```bash
# Production environment variables
export NODE_ENV=production
export PORT=443
export SSL_CERT_PATH=/path/to/cert.pem
export SSL_KEY_PATH=/path/to/key.pem
export CORS_ORIGIN=https://yourdomain.com
```

#### 3. SSL/TLS Setup
```javascript
// server.js modifications for HTTPS
const https = require('https');
const fs = require('fs');

const options = {
  cert: fs.readFileSync(process.env.SSL_CERT_PATH),
  key: fs.readFileSync(process.env.SSL_KEY_PATH)
};

const server = https.createServer(options, app);
```

### Cloud Deployment Options

#### 1. Heroku Deployment
```bash
# Install Heroku CLI
npm install -g heroku

# Create Heroku app
heroku create video-stream-signaling

# Configure environment
heroku config:set NODE_ENV=production
heroku config:set PORT=443

# Deploy
git push heroku main
```

**Procfile**:
```
web: node signaling-server/server.js
```

#### 2. AWS EC2 Deployment
```bash
# Launch EC2 instance (Ubuntu 20.04 LTS)
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Deploy application
git clone <repository>
cd video-stream-extension/signaling-server
npm install --production

# Start with PM2
pm2 start server.js --name "signaling-server"
pm2 startup
pm2 save
```

#### 3. Docker Deployment
**Dockerfile**:
```dockerfile
FROM node:16-alpine

WORKDIR /app
COPY signaling-server/package*.json ./
RUN npm ci --only=production

COPY signaling-server/ .

EXPOSE 8080
CMD ["node", "server.js"]
```

**docker-compose.yml**:
```yaml
version: '3.8'
services:
  signaling-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
```

### Load Balancing and Scaling

#### 1. Nginx Configuration
```nginx
upstream signaling_servers {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://signaling_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 2. Horizontal Scaling
For multiple server instances:
- Use Redis for session storage
- Implement sticky sessions for WebSocket connections
- Consider WebSocket clustering solutions

### Security Configuration

#### 1. CORS Configuration
```javascript
// Restrict CORS in production
app.use(cors({
  origin: [
    'https://yourdomain.com',
    'chrome-extension://*'
  ],
  credentials: true
}));
```

#### 2. Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use(limiter);
```

#### 3. WebSocket Security
```javascript
// Validate WebSocket connections
wss.on('connection', (ws, req) => {
  // Validate origin
  const origin = req.headers.origin;
  if (!isValidOrigin(origin)) {
    ws.close(1008, 'Invalid origin');
    return;
  }
  
  // Implement authentication if needed
  // Rate limit connections per IP
});
```

### Monitoring and Logging

#### 1. Application Monitoring
```javascript
// Add health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    connections: wss.clients.size
  });
});
```

#### 2. Logging Configuration
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

#### 3. Performance Monitoring
- Use tools like New Relic, DataDog, or Prometheus
- Monitor WebSocket connection counts
- Track message throughput
- Monitor server resource usage

### Backup and Recovery

#### 1. Configuration Backup
- Store configuration in version control
- Backup SSL certificates securely
- Document deployment procedures

#### 2. Disaster Recovery
- Implement automated deployments
- Use infrastructure as code (Terraform, CloudFormation)
- Test recovery procedures regularly

### Maintenance

#### 1. Updates and Patches
```bash
# Update dependencies
npm audit
npm update

# Security patches
npm audit fix

# Test in staging environment first
# Deploy to production with zero downtime
```

#### 2. Monitoring Checklist
- Server uptime and response times
- WebSocket connection stability
- SSL certificate expiration
- Resource usage trends
- Error rates and patterns

### Production Checklist

Before going live:
- [ ] SSL/TLS certificates configured
- [ ] CORS properly restricted
- [ ] Rate limiting implemented
- [ ] Logging configured
- [ ] Monitoring setup
- [ ] Backup procedures tested
- [ ] Security audit completed
- [ ] Load testing performed
- [ ] Documentation updated
- [ ] Support procedures defined

### Troubleshooting Production Issues

#### Common Production Issues
1. **WebSocket Connection Failures**
   - Check SSL certificate validity
   - Verify firewall rules
   - Monitor connection limits

2. **High CPU/Memory Usage**
   - Profile application performance
   - Check for memory leaks
   - Consider scaling horizontally

3. **SSL/TLS Issues**
   - Verify certificate chain
   - Check cipher suite compatibility
   - Monitor certificate expiration

#### Debug Tools for Production
- Server logs analysis
- Network monitoring tools
- Application performance monitoring
- WebSocket connection debugging

### Support and Maintenance

#### 1. User Support
- Provide clear documentation
- Create troubleshooting guides
- Set up support channels

#### 2. Ongoing Maintenance
- Regular security updates
- Performance optimization
- Feature updates based on user feedback
- Compatibility testing with new Chrome versions

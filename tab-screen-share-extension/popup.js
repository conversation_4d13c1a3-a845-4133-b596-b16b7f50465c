// Popup JavaScript for Tab Screen Share Extension
class TabScreenSharePopup {
  constructor() {
    this.currentTab = null;
    this.isCapturing = false;
    this.captureStartTime = null;
    this.updateInterval = null;
    this.setupEventListeners();
    this.initialize();
  }

  setupEventListeners() {
    // Button event listeners
    document.getElementById('startCaptureBtn').addEventListener('click', () => {
      this.startCapture();
    });

    document.getElementById('stopCaptureBtn').addEventListener('click', () => {
      this.stopCapture();
    });
  }

  async initialize() {
    try {
      // Get current tab information
      await this.loadCurrentTab();
      
      // Check if already capturing
      await this.checkCaptureStatus();
      
      // Update UI
      this.updateUI();
      
      this.log('Extension popup initialized');
    } catch (error) {
      this.log(`Initialization error: ${error.message}`, 'error');
    }
  }

  async loadCurrentTab() {
    try {
      const response = await this.sendMessage({ action: 'getActiveTab' });
      if (response.success) {
        this.currentTab = response.data;
        this.updateTabInfo();
      }
    } catch (error) {
      this.log(`Failed to load tab info: ${error.message}`, 'error');
    }
  }

  async checkCaptureStatus() {
    if (!this.currentTab) return;

    try {
      const response = await this.sendMessage({
        action: 'getCaptureStatus',
        tabId: this.currentTab.id
      });

      if (response.success && response.data.isCapturing) {
        this.isCapturing = true;
        this.captureStartTime = response.data.startTime;
        this.updateCaptureInfo(response.data);
        this.startPeriodicUpdates();
      }
    } catch (error) {
      this.log(`Failed to check capture status: ${error.message}`, 'error');
    }
  }

  updateTabInfo() {
    if (this.currentTab) {
      document.getElementById('tabUrl').textContent = this.currentTab.url;
      document.getElementById('tabTitle').textContent = this.currentTab.title;
    }
  }

  async startCapture() {
    if (!this.currentTab) {
      this.log('No active tab found', 'error');
      return;
    }

    try {
      this.log('Starting tab capture...');
      
      const response = await this.sendMessage({
        action: 'startCapture',
        tabId: this.currentTab.id
      });

      if (response.success) {
        this.isCapturing = true;
        this.captureStartTime = response.data.startTime;
        
        this.updateUI();
        this.updateCaptureInfo(response.data);
        this.startPeriodicUpdates();
        
        this.log('Tab capture started successfully', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to start capture: ${error.message}`, 'error');
    }
  }

  async stopCapture() {
    if (!this.currentTab) return;

    try {
      this.log('Stopping tab capture...');
      
      const response = await this.sendMessage({
        action: 'stopCapture',
        tabId: this.currentTab.id
      });

      if (response.success) {
        this.isCapturing = false;
        this.captureStartTime = null;
        
        this.stopPeriodicUpdates();
        this.updateUI();
        this.clearCaptureInfo();
        
        this.log('Tab capture stopped', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to stop capture: ${error.message}`, 'error');
    }
  }

  updateUI() {
    // Update status indicator
    const statusIndicator = document.getElementById('captureStatus');
    const statusDot = statusIndicator.querySelector('.status-dot');
    const statusText = statusIndicator.querySelector('.status-text');
    
    if (this.isCapturing) {
      statusDot.classList.add('capturing');
      statusText.textContent = 'Capturing';
    } else {
      statusDot.classList.remove('capturing');
      statusText.textContent = 'Not Capturing';
    }

    // Update button states
    document.getElementById('startCaptureBtn').disabled = this.isCapturing;
    document.getElementById('stopCaptureBtn').disabled = !this.isCapturing;

    // Update video overlay
    const videoOverlay = document.getElementById('videoOverlay');
    if (this.isCapturing) {
      videoOverlay.classList.add('hidden');
    } else {
      videoOverlay.classList.remove('hidden');
    }
  }

  updateCaptureInfo(data) {
    document.getElementById('captureStatusText').textContent = 
      data.isCapturing ? 'Active' : 'Inactive';
    
    document.getElementById('audioTracks').textContent = data.audioTracks || 0;
    document.getElementById('videoTracks').textContent = data.videoTracks || 0;
    
    if (data.duration) {
      this.updateDuration(data.duration);
    }
  }

  clearCaptureInfo() {
    document.getElementById('captureStatusText').textContent = 'Not capturing';
    document.getElementById('captureDuration').textContent = '-';
    document.getElementById('audioTracks').textContent = '-';
    document.getElementById('videoTracks').textContent = '-';
  }

  updateDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    const formattedDuration = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    document.getElementById('captureDuration').textContent = formattedDuration;
  }

  startPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    this.updateInterval = setInterval(async () => {
      if (this.isCapturing && this.currentTab) {
        try {
          const response = await this.sendMessage({
            action: 'getCaptureStatus',
            tabId: this.currentTab.id
          });

          if (response.success) {
            if (response.data.isCapturing) {
              this.updateCaptureInfo(response.data);
            } else {
              // Capture stopped externally
              this.isCapturing = false;
              this.captureStartTime = null;
              this.stopPeriodicUpdates();
              this.updateUI();
              this.clearCaptureInfo();
              this.log('Capture stopped externally', 'info');
            }
          }
        } catch (error) {
          // Silently handle errors in periodic updates
        }
      }
    }, 1000); // Update every second
  }

  stopPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  log(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('p');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Keep only last 20 log entries
    while (logContainer.children.length > 20) {
      logContainer.removeChild(logContainer.firstChild);
    }
    
    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new TabScreenSharePopup();
});

# Complex Multi-Tab Communication Example

This example demonstrates the most advanced postMessage scenario: **Tab 1** communicating with child windows that were opened by **Tab 2**, showcasing complex message routing and network-like communication patterns.

## 🎯 What This Example Demonstrates

- **Complex message routing** between multiple tabs and windows
- **Network-like communication** with node discovery and management
- **Cross-tab child window control** (Tab 1 controls windows opened by Tab 2)
- **Message relay patterns** through intermediate nodes
- **Distributed system concepts** in browser environment
- **Advanced debugging** and network monitoring

## 🏗️ Architecture

```
Tab 1 (Communicator)           Tab 2 (Window Manager)
├── Sends messages             ├── Opens child windows
├── Discovers network nodes    ├── Manages window lifecycle
├── Monitors network status    ├── Relays messages to children
└── Initiates communication   └── Reports network status
                                      ↓
                              Child Windows
                              ├── Receive relayed messages
                              ├── Send responses back
                              ├── Report status
                              └── Handle emergency signals
```

## 🚀 How to Test

### 1. Start the Server
```bash
# From the project root
npm run start-servers
```

### 2. Open the Tabs in Order
1. **Tab 1**: `http://localhost:3001/multi-tab-complex/tab1.html`
2. **Tab 2**: `http://localhost:3001/multi-tab-complex/tab2.html`

### 3. Set Up the Network
1. **In Tab 2**: Click "Open Child Window" or "Open 3 Windows"
2. **Observe**: Tab 1 automatically detects the new child windows
3. **In Tab 1**: See the network map populate with discovered nodes

### 4. Test Complex Communication
1. **Tab 1 → Children**: Send messages from Tab 1 to child windows
2. **Observe routing**: Messages go Tab 1 → Tab 2 → Children
3. **Test features**:
   - Send data packets
   - Ping network (latency testing)
   - Emergency signals
   - Network information requests

## 📡 Communication Flow

### Message Routing Patterns

#### 1. Tab 1 → Child Windows
```
Tab 1 sends "message_to_children"
    ↓ (BroadcastChannel)
Tab 2 receives and relays as "relayed_message"
    ↓ (postMessage)
Child Windows receive relayed message
```

#### 2. Child → Network
```
Child sends response
    ↓ (postMessage)
Tab 2 receives and relays to network
    ↓ (BroadcastChannel)
Tab 1 receives child response
```

#### 3. Network Discovery
```
Tab joins network → Announces capabilities
    ↓
Other tabs discover new node
    ↓
Network map updates automatically
```

## 🌐 Network Protocol

### Node Types
- **Communicator** (Tab 1): Initiates communication, monitors network
- **Window Manager** (Tab 2): Opens/manages child windows, relays messages
- **Child Window**: Receives messages, sends responses

### Message Types

#### Network Management
- `node_announcement` - Node joins network
- `node_disconnect` - Node leaves network
- `heartbeat` - Keep-alive signals
- `network_info_request/response` - Network status queries

#### Communication
- `message_to_children` - Broadcast to child windows
- `relayed_message` - Message relayed through intermediate node
- `data_packet` - Structured data transmission
- `ping_request/response` - Latency measurement

#### Emergency
- `emergency_signal` - High-priority broadcast
- `emergency_response/ack` - Emergency acknowledgments

## 🔒 Security Considerations

### Multi-Layer Validation
```javascript
// Tab 1 validates BroadcastChannel messages
if (event.origin !== window.location.origin) return;

// Tab 2 validates both network and child messages
if (!ALLOWED_ORIGINS.includes(event.origin)) return;

// Child validates parent messages
if (event.origin !== parentOrigin) return;
```

### Message Authentication
```javascript
// Each message includes sender identification
const messageData = {
    type: 'message_type',
    payload: { /* data */ },
    senderId: TAB_ID,
    senderName: TAB_NAME,
    senderRole: TAB_ROLE,
    messageId: ++messageCounter,
    timestamp: Date.now()
};
```

### Network Isolation
- All communication within same origin
- BroadcastChannel for tab-to-tab
- postMessage for parent-child
- Automatic cleanup of stale connections

## 🐛 Advanced Debugging Features

### Network Monitoring
- **Real-time network map** showing all connected nodes
- **Node status tracking** (online/offline detection)
- **Message flow visualization** in logs
- **Latency measurement** between nodes

### Comprehensive Logging
```javascript
// Each node logs with context
log(`📤 Sent via ${method}: "${message}"`, 'send');
log(`📥 Received from ${sender}: ${type}`, 'receive');
log(`🔄 Relayed message to ${count} children`, 'relay');
```

### Performance Metrics
- Message send/receive counters
- Network latency measurements
- Connection uptime tracking
- Window lifecycle monitoring

## ⚙️ Advanced Features

### Automatic Node Discovery
```javascript
// Nodes announce themselves on join
function announceNode() {
    const announcement = {
        type: 'node_announcement',
        payload: {
            role: TAB_ROLE,
            capabilities: ['messaging', 'data_processing'],
            version: '1.0.0'
        }
    };
    sendBroadcast(announcement);
}
```

### Message Relay System
```javascript
// Tab 2 relays messages from Tab 1 to children
function relayMessageToChildren(messageData) {
    const relayData = {
        type: 'relayed_message',
        payload: {
            originalMessage: messageData.payload.message,
            originalSender: messageData.senderName,
            relayedBy: TAB_NAME
        }
    };
    
    // Send to all child windows
    childWindows.forEach(child => {
        child.window.postMessage(relayData, origin);
    });
}
```

### Emergency Broadcasting
```javascript
// Emergency signals propagate through entire network
function sendEmergencySignal() {
    const emergency = {
        type: 'emergency_signal',
        payload: {
            message: 'Emergency broadcast',
            level: 'high',
            action: 'acknowledge'
        }
    };
    
    // Broadcast to all network nodes
    sendBroadcast(emergency);
}
```

### Latency Measurement
```javascript
// Ping-pong pattern for network latency
function pingNetwork() {
    const ping = {
        type: 'ping_request',
        payload: { originalTimestamp: Date.now() }
    };
    sendBroadcast(ping);
}

// Response includes round-trip calculation
function handlePingResponse(response) {
    const latency = Date.now() - response.payload.originalTimestamp;
    log(`🏓 Network latency: ${latency}ms`, 'performance');
}
```

## 📚 Learning Points

1. **Design for complexity** - Plan message routing carefully
2. **Implement discovery** - Nodes should find each other automatically
3. **Handle failures gracefully** - Network nodes can disconnect unexpectedly
4. **Monitor performance** - Track latency and message flow
5. **Provide debugging tools** - Visual network maps and comprehensive logging
6. **Use unique identifiers** - Every node and message needs unique IDs
7. **Implement cleanup** - Remove stale connections and closed windows
8. **Plan for scale** - Consider performance with many nodes

## 🚀 Real-World Applications

### Distributed Dashboard
```javascript
// Multiple tabs showing different views of same data
// One tab manages data updates, others display views
// Child windows show detailed drill-downs
```

### Collaborative Tools
```javascript
// Multiple users in different tabs/windows
// Real-time synchronization of changes
// Conflict resolution and merge strategies
```

### Monitoring Systems
```javascript
// Central monitoring tab
// Child windows for different services
// Alert propagation and acknowledgment
```

### Multi-Window Applications
```javascript
// Main application window
// Tool palettes and property panels
// Document windows and previews
```

## 🔧 Customization

### Adding New Node Types
```javascript
// Define new role
const TAB_ROLE = 'data_processor';

// Add role-specific capabilities
const capabilities = ['data_processing', 'analytics', 'reporting'];

// Handle role-specific messages
switch (senderRole) {
    case 'data_processor':
        handleDataProcessorMessage(data);
        break;
}
```

### Custom Message Types
```javascript
// Define new message type
case 'custom_data_sync':
    handleDataSync(payload);
    break;

// Send custom message
function sendDataSync(data) {
    const syncMessage = {
        type: 'custom_data_sync',
        payload: { syncData: data },
        // ... standard fields
    };
    sendBroadcast(syncMessage);
}
```

### Network Topology Changes
```javascript
// Implement different routing strategies
// Direct communication vs relay patterns
// Hub-and-spoke vs mesh networks
// Load balancing across multiple relays
```

This example demonstrates enterprise-level communication patterns that can be applied to complex web applications requiring coordination between multiple browser contexts.

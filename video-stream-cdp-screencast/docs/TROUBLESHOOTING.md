# Troubleshooting Guide

This guide helps resolve common issues when running the CDP Screencast POC system.

## Quick Diagnostics

### 1. Run the Test Suite
```bash
cd video-stream-cdp-screencast
node scripts/test.js
```

### 2. Check System Status
```bash
# Check if all components are running
curl http://localhost:3000/api/status

# Check available targets
curl http://localhost:3000/api/targets
```

### 3. Check Logs
- **Signaling Server**: Console output from the server process
- **Client UI**: Browser DevTools console (F12)
- **CDP Manager**: Browser DevTools console in the manager tab

## Common Issues

### 🚫 Port Already in Use

**Error**: `Port 3000/8080/9222 is not available`

**Solutions**:
1. **Find and kill the process using the port**:
   ```bash
   # Find process using port 3000
   lsof -i :3000
   # Kill the process
   kill -9 <PID>
   ```

2. **Change the port configuration**:
   ```bash
   # Edit .env file
   SIGNALING_PORT=3001
   CLIENT_PORT=8081
   CDP_PORT=9223
   ```

### 🌐 Chrome Not Found

**Error**: `Chrome browser not found`

**Solutions**:
1. **Install Chrome**:
   - Download from: https://www.google.com/chrome/
   - Or install via package manager:
     ```bash
     # macOS
     brew install --cask google-chrome
     
     # Ubuntu/Debian
     wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
     sudo apt-get update
     sudo apt-get install google-chrome-stable
     ```

2. **Set Chrome path manually**:
   ```bash
   # Edit .env file
   CHROME_PATH="/path/to/your/chrome"
   ```

### 🔌 WebSocket Connection Failed

**Error**: `Failed to connect to signaling server`

**Solutions**:
1. **Check if signaling server is running**:
   ```bash
   curl http://localhost:3000/health
   ```

2. **Check firewall settings**:
   - Ensure ports 3000 and 8080 are not blocked
   - Disable firewall temporarily for testing

3. **Check browser console for CORS errors**:
   - Open DevTools (F12) → Console tab
   - Look for CORS or WebSocket errors

### 📺 No Video Stream

**Error**: Video display shows "Connecting..." indefinitely

**Solutions**:
1. **Check CDP Manager status**:
   - Open Chrome DevTools in the manager tab
   - Look for CDP connection errors in console

2. **Verify target selection**:
   - Ensure a target is selected in the client UI
   - Check that the target tab is fully loaded

3. **Check WebRTC connection**:
   - Open browser DevTools → Console
   - Look for WebRTC connection errors

### 🎯 No Targets Available

**Error**: "No targets available" in client UI

**Solutions**:
1. **Check browser launch**:
   ```bash
   # Manually launch Chrome with CDP
   google-chrome --remote-debugging-port=9222 --disable-web-security
   ```

2. **Verify CDP connection**:
   ```bash
   # Check CDP endpoint
   curl http://localhost:9222/json
   ```

3. **Check target URLs**:
   - Ensure target websites are accessible
   - Check for network connectivity issues

### 🔧 CDP Controller Not Working

**Error**: Manager tab shows errors or doesn't connect to CDP

**Solutions**:
1. **Check script injection**:
   - Look for script errors in manager tab console
   - Verify all CDP controller files exist

2. **Verify CDP WebSocket URL**:
   ```javascript
   // In manager tab console
   console.log('CDP URL:', `ws://localhost:9222/json`);
   ```

3. **Check CDP permissions**:
   - Ensure Chrome was launched with `--remote-debugging-port`
   - Verify `--disable-web-security` flag is set

## Performance Issues

### 🐌 Slow Frame Rate

**Symptoms**: Video stream is choppy or has low frame rate

**Solutions**:
1. **Adjust quality settings**:
   - Lower the quality slider in client UI
   - Reduce resolution to 854x480 or lower

2. **Check system resources**:
   ```bash
   # Monitor CPU and memory usage
   top
   # Or on macOS
   Activity Monitor
   ```

3. **Optimize screencast settings**:
   ```javascript
   // In CDP controller, adjust these values
   screencastConfig: {
     quality: 60,        // Lower quality
     maxWidth: 854,      // Lower resolution
     maxHeight: 480,
     everyNthFrame: 2    // Skip frames
   }
   ```

### 🔥 High CPU Usage

**Symptoms**: System becomes slow, high CPU usage

**Solutions**:
1. **Reduce video quality**:
   - Use lower quality settings
   - Reduce frame rate

2. **Close unnecessary tabs**:
   - Keep only essential target tabs open
   - Close other Chrome windows

3. **Monitor resource usage**:
   ```bash
   # Check Node.js processes
   ps aux | grep node
   ```

## Network Issues

### 🌍 WebRTC Connection Failed

**Error**: WebRTC peer connection fails to establish

**Solutions**:
1. **Check STUN servers**:
   ```javascript
   // Test STUN server connectivity
   const pc = new RTCPeerConnection({
     iceServers: [{ urls: 'stun:stun.cloudflare.com:3478' }]
   });
   ```

2. **Configure TURN servers** (for NAT traversal):
   ```javascript
   // Add to WebRTC configuration
   iceServers: [
     { urls: 'stun:stun.cloudflare.com:3478' },
     {
       urls: 'turn:your-turn-server.com:3478',
       username: 'your-username',
       credential: 'your-password'
     }
   ]
   ```

3. **Check network connectivity**:
   ```bash
   # Test connectivity to STUN server
   nc -u stun.cloudflare.com 3478
   ```

## Development Issues

### 🔨 Build/Setup Errors

**Error**: Setup script fails or dependencies can't be installed

**Solutions**:
1. **Clear npm cache**:
   ```bash
   npm cache clean --force
   ```

2. **Delete node_modules and reinstall**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Check Node.js version**:
   ```bash
   node --version  # Should be 18+
   ```

### 📝 Script Errors

**Error**: JavaScript errors in browser console

**Solutions**:
1. **Check for syntax errors**:
   - Review browser console for specific error messages
   - Verify all script files are loaded correctly

2. **Clear browser cache**:
   - Hard refresh: Ctrl+Shift+R (or Cmd+Shift+R on macOS)
   - Clear browser cache and cookies

3. **Check file permissions**:
   ```bash
   # Ensure scripts are readable
   chmod +r cdp-controller/*.js
   chmod +r client-ui/*.js
   ```

## Getting Help

### 📊 Collect Debug Information

Before reporting issues, collect this information:

1. **System Information**:
   ```bash
   node --version
   npm --version
   google-chrome --version
   ```

2. **Error Logs**:
   - Server console output
   - Browser console errors
   - Network tab in DevTools

3. **Configuration**:
   ```bash
   cat .env
   curl http://localhost:3000/api/status
   ```

### 🔍 Enable Debug Mode

Set debug mode for detailed logging:

```bash
# Edit .env file
DEBUG=true
```

### 📞 Support Channels

1. **Check the README.md** for basic setup instructions
2. **Review ARCHITECTURE.md** for system design details
3. **Run the test suite** to identify specific issues
4. **Check browser DevTools** for client-side errors
5. **Review server logs** for backend issues

## Advanced Debugging

### 🕵️ CDP Protocol Debugging

Monitor CDP messages:

```javascript
// In manager tab console
window.cdpManager.debug = true;
```

### 🔬 WebRTC Debugging

Monitor WebRTC statistics:

```javascript
// In client UI console
window.cdpApp.webrtcHandler.debug = true;
```

### 📡 Network Traffic Analysis

Use browser DevTools Network tab to monitor:
- WebSocket messages
- HTTP API calls
- Resource loading issues

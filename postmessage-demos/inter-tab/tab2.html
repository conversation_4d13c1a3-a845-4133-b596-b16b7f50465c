<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inter-Tab Communication - Tab 2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }
        .tab-header {
            text-align: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin: 15px 0;
            align-items: center;
        }
        input[type="text"] {
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #a29bfe;
            color: white;
        }
        .btn-secondary {
            background: #ffeaa7;
            color: #333;
        }
        .btn-danger {
            background: #fab1a0;
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #ffeaa7;
        }
        .log {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .connected-tabs {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .tab-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .tab-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .method-selector {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .method-btn {
            padding: 8px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: transparent;
            color: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .method-btn.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: #ffeaa7;
        }
        .special-controls {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="tab-header">
            <h1>🌸 Inter-Tab Communication - Tab 2</h1>
            <p>This tab demonstrates advanced inter-tab communication patterns</p>
            <p><strong>Tab ID:</strong> <span id="tabId"></span></p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h4>📡 BroadcastChannel</h4>
                <div id="broadcastStatus">Initializing...</div>
            </div>
            <div class="status-card">
                <h4>💾 LocalStorage</h4>
                <div id="storageStatus">Ready</div>
            </div>
            <div class="status-card">
                <h4>🔗 SharedWorker</h4>
                <div id="workerStatus">Initializing...</div>
            </div>
        </div>

        <div class="connected-tabs">
            <h4>🔗 Connected Tabs</h4>
            <div id="connectedTabsList" class="tab-list">
                <div class="tab-item">No other tabs detected</div>
            </div>
        </div>

        <div class="container">
            <h3>📤 Send Message</h3>
            <div class="method-selector">
                <button class="method-btn active" data-method="broadcast">BroadcastChannel</button>
                <button class="method-btn" data-method="storage">LocalStorage</button>
                <button class="method-btn" data-method="worker">SharedWorker</button>
            </div>
            
            <div class="controls">
                <input type="text" id="messageInput" placeholder="Enter message to send to other tabs..." value="Hello from Tab 2!">
                <button class="btn-primary" onclick="sendMessage()">Send Message</button>
            </div>
            
            <div class="controls">
                <button class="btn-secondary" onclick="sendHeartbeat()">Send Heartbeat</button>
                <button class="btn-secondary" onclick="requestTabList()">Request Tab List</button>
                <button class="btn-danger" onclick="clearAllData()">Clear All Data</button>
            </div>
        </div>

        <div class="container special-controls">
            <h3>🎯 Special Tab 2 Features</h3>
            <div class="controls">
                <button class="btn-primary" onclick="sendBulkMessages()">Send Bulk Messages</button>
                <button class="btn-secondary" onclick="sendFormattedData()">Send Formatted Data</button>
            </div>
            <div class="controls">
                <button class="btn-secondary" onclick="simulateTyping()">Simulate Typing</button>
                <button class="btn-danger" onclick="sendEmergencyBroadcast()">Emergency Broadcast</button>
            </div>
        </div>

        <div class="container">
            <h3>📋 Communication Log</h3>
            <div id="messageLog" class="log"></div>
            <button onclick="clearLog()" style="margin-top: 10px; padding: 8px 16px; background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 4px;">Clear Log</button>
        </div>
    </div>

    <script>
        // Tab identification
        const TAB_ID = 'tab2_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const TAB_NAME = 'Tab 2';
        
        // Communication channels
        let broadcastChannel;
        let sharedWorker;
        let currentMethod = 'broadcast';
        
        // State
        let connectedTabs = new Map();
        let messageCounter = 0;
        let typingInterval;
        
        // DOM elements
        const messageInput = document.getElementById('messageInput');
        const messageLog = document.getElementById('messageLog');
        const connectedTabsList = document.getElementById('connectedTabsList');
        
        // Initialize
        document.getElementById('tabId').textContent = TAB_ID;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            console.log(`Tab2: ${message}`);
        }
        
        function updateStatus(element, status, isConnected = true) {
            const statusElement = document.getElementById(element);
            statusElement.textContent = status;
            statusElement.style.color = isConnected ? '#a29bfe' : '#fab1a0';
        }
        
        // Initialize BroadcastChannel
        function initBroadcastChannel() {
            try {
                broadcastChannel = new BroadcastChannel('inter-tab-demo');
                broadcastChannel.addEventListener('message', handleBroadcastMessage);
                updateStatus('broadcastStatus', '✅ Connected');
                log('🔗 BroadcastChannel initialized', 'success');
            } catch (error) {
                updateStatus('broadcastStatus', '❌ Not supported', false);
                log(`❌ BroadcastChannel error: ${error.message}`, 'error');
            }
        }
        
        // Initialize SharedWorker (same as Tab 1)
        function initSharedWorker() {
            try {
                const workerScript = `
                    const connections = new Set();
                    
                    self.addEventListener('connect', function(e) {
                        const port = e.ports[0];
                        connections.add(port);
                        
                        port.addEventListener('message', function(event) {
                            connections.forEach(p => {
                                if (p !== port) {
                                    try {
                                        p.postMessage(event.data);
                                    } catch (err) {
                                        connections.delete(p);
                                    }
                                }
                            });
                        });
                        
                        port.start();
                        
                        port.addEventListener('close', function() {
                            connections.delete(port);
                        });
                    });
                `;
                
                const blob = new Blob([workerScript], { type: 'application/javascript' });
                const workerUrl = URL.createObjectURL(blob);
                
                sharedWorker = new SharedWorker(workerUrl);
                sharedWorker.port.addEventListener('message', handleWorkerMessage);
                sharedWorker.port.start();
                
                updateStatus('workerStatus', '✅ Connected');
                log('🔗 SharedWorker initialized', 'success');
            } catch (error) {
                updateStatus('workerStatus', '❌ Not supported', false);
                log(`❌ SharedWorker error: ${error.message}`, 'error');
            }
        }
        
        // Message handlers (same as Tab 1)
        function handleBroadcastMessage(event) {
            const data = event.data;
            log(`📡 BroadcastChannel received: ${data.type}`, 'receive');
            processMessage(data, 'broadcast');
        }
        
        function handleWorkerMessage(event) {
            const data = event.data;
            log(`🔗 SharedWorker received: ${data.type}`, 'receive');
            processMessage(data, 'worker');
        }
        
        function handleStorageMessage(event) {
            if (event.key === 'inter-tab-message') {
                try {
                    const data = JSON.parse(event.newValue);
                    if (data.senderId !== TAB_ID) {
                        log(`💾 LocalStorage received: ${data.type}`, 'receive');
                        processMessage(data, 'storage');
                    }
                } catch (error) {
                    log(`❌ LocalStorage parse error: ${error.message}`, 'error');
                }
            }
        }
        
        // Process incoming messages
        function processMessage(data, method) {
            const { type, payload, senderId, senderName, timestamp } = data;
            
            switch (type) {
                case 'chat_message':
                    log(`💬 Message from ${senderName}: "${payload.message}"`, 'message');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'heartbeat':
                    log(`💓 Heartbeat from ${senderName}`, 'heartbeat');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'tab_list_request':
                    log(`📋 Tab list request from ${senderName}`, 'request');
                    sendTabInfo(method);
                    break;
                    
                case 'tab_info':
                    log(`📊 Tab info from ${senderName}`, 'info');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'tab_disconnect':
                    log(`👋 ${senderName} disconnected`, 'disconnect');
                    removeConnectedTab(senderId);
                    break;
                    
                case 'bulk_message':
                    log(`📦 Bulk message ${payload.index}/${payload.total} from ${senderName}: "${payload.message}"`, 'bulk');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'formatted_data':
                    log(`📊 Formatted data from ${senderName}:`, 'data');
                    log(`  Title: ${payload.title}`, 'data');
                    log(`  Items: ${payload.items.length}`, 'data');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'typing_indicator':
                    log(`⌨️ ${senderName} is typing...`, 'typing');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                case 'emergency':
                    log(`🚨 EMERGENCY from ${senderName}: ${payload.message}`, 'emergency');
                    updateConnectedTab(senderId, senderName, method);
                    break;
                    
                default:
                    log(`⚠️ Unknown message type: ${type}`, 'warning');
            }
        }
        
        // Connected tabs management (same as Tab 1)
        function updateConnectedTab(tabId, tabName, method) {
            connectedTabs.set(tabId, {
                name: tabName,
                method: method,
                lastSeen: Date.now()
            });
            updateConnectedTabsDisplay();
        }
        
        function removeConnectedTab(tabId) {
            connectedTabs.delete(tabId);
            updateConnectedTabsDisplay();
        }
        
        function updateConnectedTabsDisplay() {
            if (connectedTabs.size === 0) {
                connectedTabsList.innerHTML = '<div class="tab-item">No other tabs detected</div>';
            } else {
                connectedTabsList.innerHTML = Array.from(connectedTabs.entries())
                    .map(([id, info]) => `<div class="tab-item">${info.name} (${info.method})</div>`)
                    .join('');
            }
        }
        
        // Basic messaging functions (same as Tab 1)
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) {
                log('❌ Cannot send empty message', 'error');
                return;
            }
            
            const messageData = {
                type: 'chat_message',
                payload: { message: message },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendViaMethod(messageData, currentMethod);
            log(`📤 Sent via ${currentMethod}: "${message}"`, 'send');
        }
        
        function sendHeartbeat() {
            const heartbeatData = {
                type: 'heartbeat',
                payload: {},
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendViaMethod(heartbeatData, currentMethod);
            log(`💓 Sent heartbeat via ${currentMethod}`, 'send');
        }
        
        function requestTabList() {
            const requestData = {
                type: 'tab_list_request',
                payload: {},
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendViaMethod(requestData, currentMethod);
            log(`📋 Requested tab list via ${currentMethod}`, 'send');
        }
        
        function sendTabInfo(method) {
            const infoData = {
                type: 'tab_info',
                payload: {
                    url: window.location.href,
                    userAgent: navigator.userAgent.substring(0, 50) + '...'
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendViaMethod(infoData, method);
            log(`📊 Sent tab info via ${method}`, 'send');
        }
        
        // Special Tab 2 functions
        function sendBulkMessages() {
            const messages = [
                'First bulk message',
                'Second bulk message',
                'Third bulk message',
                'Fourth bulk message',
                'Final bulk message'
            ];
            
            messages.forEach((message, index) => {
                setTimeout(() => {
                    const bulkData = {
                        type: 'bulk_message',
                        payload: { 
                            message: message,
                            index: index + 1,
                            total: messages.length
                        },
                        senderId: TAB_ID,
                        senderName: TAB_NAME,
                        messageId: ++messageCounter,
                        timestamp: Date.now()
                    };
                    
                    sendViaMethod(bulkData, currentMethod);
                    log(`📦 Sent bulk message ${index + 1}/${messages.length}`, 'send');
                }, index * 500);
            });
        }
        
        function sendFormattedData() {
            const formattedData = {
                type: 'formatted_data',
                payload: {
                    title: 'Sample Data Report',
                    timestamp: new Date().toISOString(),
                    items: [
                        { id: 1, name: 'Item A', value: 100 },
                        { id: 2, name: 'Item B', value: 200 },
                        { id: 3, name: 'Item C', value: 300 }
                    ],
                    summary: {
                        totalItems: 3,
                        totalValue: 600
                    }
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendViaMethod(formattedData, currentMethod);
            log(`📊 Sent formatted data report`, 'send');
        }
        
        function simulateTyping() {
            let count = 0;
            const maxCount = 5;
            
            typingInterval = setInterval(() => {
                if (count >= maxCount) {
                    clearInterval(typingInterval);
                    return;
                }
                
                const typingData = {
                    type: 'typing_indicator',
                    payload: { isTyping: true },
                    senderId: TAB_ID,
                    senderName: TAB_NAME,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                sendViaMethod(typingData, currentMethod);
                log(`⌨️ Sent typing indicator ${count + 1}/${maxCount}`, 'send');
                count++;
            }, 1000);
        }
        
        function sendEmergencyBroadcast() {
            const emergencyData = {
                type: 'emergency',
                payload: { 
                    message: 'This is an emergency broadcast test from Tab 2!',
                    level: 'high',
                    timestamp: new Date().toISOString()
                },
                senderId: TAB_ID,
                senderName: TAB_NAME,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            // Send via all available methods for emergency
            ['broadcast', 'storage', 'worker'].forEach(method => {
                sendViaMethod(emergencyData, method);
            });
            
            log(`🚨 Sent emergency broadcast via all methods`, 'send');
        }
        
        // Send via specific method (same as Tab 1)
        function sendViaMethod(data, method) {
            try {
                switch (method) {
                    case 'broadcast':
                        if (broadcastChannel) {
                            broadcastChannel.postMessage(data);
                        } else {
                            throw new Error('BroadcastChannel not available');
                        }
                        break;
                        
                    case 'storage':
                        localStorage.setItem('inter-tab-message', JSON.stringify(data));
                        setTimeout(() => {
                            localStorage.removeItem('inter-tab-message');
                        }, 100);
                        break;
                        
                    case 'worker':
                        if (sharedWorker) {
                            sharedWorker.port.postMessage(data);
                        } else {
                            throw new Error('SharedWorker not available');
                        }
                        break;
                        
                    default:
                        throw new Error(`Unknown method: ${method}`);
                }
            } catch (error) {
                log(`❌ Failed to send via ${method}: ${error.message}`, 'error');
            }
        }
        
        function clearAllData() {
            localStorage.clear();
            connectedTabs.clear();
            updateConnectedTabsDisplay();
            log('🗑️ Cleared all data', 'info');
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Event listeners (same as Tab 1)
        document.querySelectorAll('.method-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.method-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentMethod = this.dataset.method;
                log(`🔄 Switched to ${currentMethod} method`, 'info');
            });
        });
        
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
        
        window.addEventListener('storage', handleStorageMessage);
        
        window.addEventListener('beforeunload', function() {
            const disconnectData = {
                type: 'tab_disconnect',
                payload: {},
                senderId: TAB_ID,
                senderName: TAB_NAME,
                timestamp: Date.now()
            };
            
            sendViaMethod(disconnectData, 'broadcast');
            sendViaMethod(disconnectData, 'storage');
            sendViaMethod(disconnectData, 'worker');
        });
        
        // Clean up old connected tabs
        setInterval(() => {
            const now = Date.now();
            const timeout = 30000;
            
            for (const [tabId, info] of connectedTabs.entries()) {
                if (now - info.lastSeen > timeout) {
                    connectedTabs.delete(tabId);
                }
            }
            updateConnectedTabsDisplay();
        }, 5000);
        
        // Initialize everything
        initBroadcastChannel();
        initSharedWorker();
        
        // Send initial heartbeat
        setTimeout(() => {
            sendHeartbeat();
        }, 1000);
        
        log('🌸 Tab 2 initialized with special features', 'success');
    </script>
</body>
</html>

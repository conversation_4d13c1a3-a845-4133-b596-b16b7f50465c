const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");

class BrowserManager {
  constructor() {
    this.browser = null;
    this.cdpPort = process.env.CDP_PORT || 9222;
    this.debug = process.env.DEBUG === "true";
    this.managerPage = null;
    this.targetPages = new Map(); // targetId -> page

    // Default target URLs
    this.targetUrls = [
      { id: "google", url: "https://google.com", title: "Google" },
      { id: "github", url: "https://github.com", title: "GitHub" },
      {
        id: "stackoverflow",
        url: "https://stackoverflow.com",
        title: "Stack Overflow",
      },
      { id: "wikipedia", url: "https://wikipedia.org", title: "Wikipedia" },
    ];
  }

  async launch() {
    try {
      this.log("Launching Chrome browser with CDP...");

      const data = await fetch(`http://localhost:${this.cdpPort}/json/version`);
      const version = await data.json();
      const wsEndpoint = version.webSocketDebuggerUrl;

      // Connect Puppeteer to the existing Chrome instance
      this.browser = await puppeteer.connect({
        browserWSEndpoint: wsEndpoint,
        defaultViewport: { width: 1280, height: 720 },
      });

      this.log("Puppeteer connected to Chrome");

      // Create manager tab and target tabs
      await this.setupTabs();

      return {
        cdpPort: 9222,
        browserWSEndpoint: this.browser.wsEndpoint(),
        targets: this.targetUrls.length,
      };
    } catch (error) {
      this.error("Failed to launch browser:", error);
      throw error;
    }
  }

  async setupTabs() {
    try {
      this.log("Setting up browser tabs...");

      // Create manager tab first
      this.managerPage = await this.browser.newPage();

      await this.managerPage.setViewport({ width: 1280, height: 720 });

      // Navigate to a blank page and inject the CDP controller
      await this.managerPage.goto("about:blank");
      await this.managerPage.evaluate(() => {
        document.title = "CDP Manager";
        document.body.innerHTML =
          "<h1>CDP Manager Tab</h1><p>This tab manages CDP screencast streaming.</p>";
      });

      this.log("Manager tab created");

      // Create target tabs
      for (const target of this.targetUrls) {
        try {
          const page = await this.browser.newPage();

          await page.setViewport({ width: 1280, height: 720 });

          this.log(`Opening target: ${target.title} (${target.url})`);
          await page.goto(target.url, {
            waitUntil: "networkidle0",
            timeout: 30000,
          });

          this.targetPages.set(target.id, page);
          this.log(`Target ${target.title} loaded successfully`);
        } catch (error) {
          this.error(`Failed to load target ${target.title}:`, error);
          // Continue with other targets
        }
      }

      // Inject CDP controller script into manager tab
      await this.injectCDPController();

      this.log(
        `Browser setup complete. Manager + ${this.targetPages.size} target tabs`
      );
    } catch (error) {
      this.error("Failed to setup tabs:", error);
      throw error;
    }
  }

  async injectCDPController() {
    try {
      this.log("Injecting CDP controller script...");

      // Read the CDP controller script files
      const managerPath = path.join(__dirname, "../cdp-controller/manager.js");
      const screencastPath = path.join(
        __dirname,
        "../cdp-controller/screencast.js"
      );
      const webrtcPath = path.join(
        __dirname,
        "../cdp-controller/webrtc-client.js"
      );

      if (!fs.existsSync(managerPath)) {
        this.log("CDP controller script not found, skipping injection...");
        return;
      }

      // Read all script files
      const managerScript = fs.readFileSync(managerPath, "utf8");
      const screencastScript = fs.existsSync(screencastPath)
        ? fs.readFileSync(screencastPath, "utf8")
        : "";
      const webrtcScript = fs.existsSync(webrtcPath)
        ? fs.readFileSync(webrtcPath, "utf8")
        : "";

      // Combine scripts
      const combinedScript = `
        ${screencastScript}
        ${webrtcScript}
        ${managerScript}
      `;

      // Inject the script into the manager page
      await this.managerPage.evaluateOnNewDocument(combinedScript);
      await this.managerPage.evaluate(combinedScript);

      this.log("CDP controller script injected successfully");
    } catch (error) {
      this.error("Failed to inject CDP controller:", error);
      // Don't throw - this is not critical for basic functionality
    }
  }

  async getTargets() {
    try {
      const targets = [];
      // get targets call from simple cdp

      const allTargetsFromBrowser = this.browser.targets();
      const allTargets = allTargetsFromBrowser.filter(
        (target) => target.type() === "page" && target.url() !== "about:blank"
      );
      this.log(`Found ${allTargets.length} targets in browser`);

      for (const [targetId, page] of this.targetPages) {
        try {
          const title = await page.title();
          const url = page.url();

          targets.push({
            id: targetId,
            title:
              title ||
              this.targetUrls.find((t) => t.id === targetId)?.title ||
              "Unknown",
            url: url,
            type: "page",
            favicon: `${new URL(url).origin}/favicon.ico`,
          });
        } catch (error) {
          this.error(`Failed to get info for target ${targetId}:`, error);
        }
      }

      return targets;
    } catch (error) {
      this.error("Failed to get targets:", error);
      return [];
    }
  }

  async getManagerTabInfo() {
    if (!this.managerPage) return null;

    try {
      return {
        title: await this.managerPage.title(),
        url: this.managerPage.url(),
        target: this.managerPage.target(),
      };
    } catch (error) {
      this.error("Failed to get manager tab info:", error);
      return null;
    }
  }

  getStatus() {
    return {
      running: !!this.browser,
      cdpPort: this.cdpPort,
      managerTab: !!this.managerPage,
      targetTabs: this.targetPages.size,
      targets: Array.from(this.targetPages.keys()),
    };
  }

  async close() {
    try {
      this.log("Closing browser...");

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      this.managerPage = null;
      this.targetPages.clear();

      this.log("Browser closed successfully");
    } catch (error) {
      this.error("Failed to close browser:", error);
    }
  }

  log(...args) {
    if (this.debug) {
      console.log(`[BrowserManager]`, ...args);
    }
  }

  error(...args) {
    console.error(`[BrowserManager ERROR]`, ...args);
  }
}

module.exports = BrowserManager;

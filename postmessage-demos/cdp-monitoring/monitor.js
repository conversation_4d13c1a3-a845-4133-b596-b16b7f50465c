import puppeteer from 'puppeteer';
import http from 'http';
import { WebSocketServer } from 'ws';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PostMessageMonitor {
    constructor() {
        this.browser = null;
        this.pages = new Map();
        this.wsServer = null;
        this.httpServer = null;
        this.clients = new Set();
        this.messageLog = [];
        this.isMonitoring = false;
    }

    async initialize() {
        console.log('🚀 Initializing PostMessage Monitor...');
        
        // Launch browser with debugging enabled
        this.browser = await puppeteer.launch({
            headless: false,
            devtools: true,
            args: [
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--remote-debugging-port=9222'
            ]
        });

        // Set up WebSocket server for real-time communication
        this.setupWebSocketServer();
        
        // Set up HTTP server for debug UI
        this.setupHttpServer();

        console.log('✅ PostMessage Monitor initialized');
        console.log('📊 Debug UI: http://localhost:3003');
        console.log('🔌 WebSocket: ws://localhost:3004');
    }

    setupWebSocketServer() {
        this.wsServer = new WebSocketServer({ port: 3004 });
        
        this.wsServer.on('connection', (ws) => {
            console.log('🔌 Debug client connected');
            this.clients.add(ws);
            
            // Send existing message log to new client
            ws.send(JSON.stringify({
                type: 'message_history',
                data: this.messageLog
            }));

            ws.on('message', async (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    await this.handleClientMessage(message);
                } catch (error) {
                    console.error('❌ Error handling client message:', error);
                }
            });

            ws.on('close', () => {
                console.log('🔌 Debug client disconnected');
                this.clients.delete(ws);
            });
        });
    }

    setupHttpServer() {
        this.httpServer = http.createServer((req, res) => {
            if (req.url === '/') {
                this.serveDebugUI(res);
            } else if (req.url === '/api/status') {
                this.serveStatus(res);
            } else if (req.url === '/api/pages') {
                this.servePages(res);
            } else {
                res.writeHead(404);
                res.end('Not Found');
            }
        });

        this.httpServer.listen(3003, () => {
            console.log('🌐 HTTP server running on port 3003');
        });
    }

    serveDebugUI(res) {
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostMessage Monitor - Debug UI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log { background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 8px; height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #3498db; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .message-item { margin: 5px 0; padding: 5px; border-left: 3px solid #3498db; }
        .message-send { border-left-color: #27ae60; }
        .message-receive { border-left-color: #e74c3c; }
        .message-error { border-left-color: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 PostMessage Monitor - Debug UI</h1>
            <p>Real-time monitoring and debugging of postMessage communications</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalMessages">0</div>
                <div>Total Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activePages">0</div>
                <div>Active Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="errorCount">0</div>
                <div>Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgLatency">0ms</div>
                <div>Avg Latency</div>
            </div>
        </div>

        <div class="controls">
            <h3>🎛️ Monitor Controls</h3>
            <div id="connectionStatus" class="status disconnected">❌ Not connected to monitor</div>
            
            <button class="btn-primary" onclick="startMonitoring()">Start Monitoring</button>
            <button class="btn-danger" onclick="stopMonitoring()">Stop Monitoring</button>
            <button class="btn-warning" onclick="clearLog()">Clear Log</button>
            <button class="btn-success" onclick="exportLog()">Export Log</button>
            
            <h4>Quick Actions</h4>
            <button class="btn-primary" onclick="openDemoPage('cross-origin-iframe')">Open Cross-Origin Demo</button>
            <button class="btn-primary" onclick="openDemoPage('inter-tab')">Open Inter-Tab Demo</button>
            <button class="btn-primary" onclick="openDemoPage('parent-child-window')">Open Parent-Child Demo</button>
            <button class="btn-primary" onclick="openDemoPage('multi-tab-complex')">Open Complex Demo</button>
        </div>

        <div class="controls">
            <h3>📋 Message Log</h3>
            <div id="messageLog" class="log">Waiting for messages...</div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let errorCount = 0;
        let latencies = [];

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:3004');
            
            ws.onopen = () => {
                document.getElementById('connectionStatus').className = 'status connected';
                document.getElementById('connectionStatus').textContent = '✅ Connected to monitor';
                log('🔌 Connected to PostMessage Monitor');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleMonitorMessage(data);
            };
            
            ws.onclose = () => {
                document.getElementById('connectionStatus').className = 'status disconnected';
                document.getElementById('connectionStatus').textContent = '❌ Disconnected from monitor';
                log('🔌 Disconnected from monitor');
                
                // Attempt to reconnect after 3 seconds
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = (error) => {
                log('❌ WebSocket error: ' + error);
            };
        }

        function handleMonitorMessage(data) {
            switch (data.type) {
                case 'postmessage_event':
                    handlePostMessageEvent(data.data);
                    break;
                case 'message_history':
                    loadMessageHistory(data.data);
                    break;
                case 'page_update':
                    updatePageCount(data.data.count);
                    break;
                case 'error':
                    handleError(data.data);
                    break;
            }
        }

        function handlePostMessageEvent(event) {
            messageCount++;
            document.getElementById('totalMessages').textContent = messageCount;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = \`[\${timestamp}] \${event.direction.toUpperCase()}: \${event.type} | Origin: \${event.origin} | Data: \${JSON.stringify(event.data).substring(0, 100)}...\`;
            
            log(logEntry, event.direction === 'send' ? 'message-send' : 'message-receive');
            
            if (event.latency) {
                latencies.push(event.latency);
                const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
                document.getElementById('avgLatency').textContent = Math.round(avgLatency) + 'ms';
            }
        }

        function handleError(error) {
            errorCount++;
            document.getElementById('errorCount').textContent = errorCount;
            log('❌ ERROR: ' + error.message, 'message-error');
        }

        function loadMessageHistory(messages) {
            messages.forEach(msg => handlePostMessageEvent(msg));
        }

        function updatePageCount(count) {
            document.getElementById('activePages').textContent = count;
        }

        function log(message, className = '') {
            const logElement = document.getElementById('messageLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-item ' + className;
            messageDiv.textContent = message;
            logElement.appendChild(messageDiv);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function startMonitoring() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'start_monitoring' }));
                log('🚀 Started monitoring');
            }
        }

        function stopMonitoring() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'stop_monitoring' }));
                log('🛑 Stopped monitoring');
            }
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = 'Log cleared...';
            messageCount = 0;
            errorCount = 0;
            latencies = [];
            document.getElementById('totalMessages').textContent = '0';
            document.getElementById('errorCount').textContent = '0';
            document.getElementById('avgLatency').textContent = '0ms';
        }

        function exportLog() {
            const logContent = document.getElementById('messageLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'postmessage-log-' + new Date().toISOString().slice(0, 19) + '.txt';
            a.click();
            URL.revokeObjectURL(url);
        }

        function openDemoPage(demo) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ 
                    type: 'open_demo', 
                    demo: demo 
                }));
                log('🌐 Opening ' + demo + ' demo');
            }
        }

        // Initialize WebSocket connection
        connectWebSocket();
    </script>
</body>
</html>`;
        
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(html);
    }

    serveStatus(res) {
        const status = {
            isMonitoring: this.isMonitoring,
            pageCount: this.pages.size,
            messageCount: this.messageLog.length,
            uptime: process.uptime()
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(status));
    }

    servePages(res) {
        const pages = Array.from(this.pages.entries()).map(([id, page]) => ({
            id,
            url: page.url(),
            title: page.title || 'Unknown'
        }));
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(pages));
    }

    async handleClientMessage(message) {
        switch (message.type) {
            case 'start_monitoring':
                await this.startMonitoring();
                break;
            case 'stop_monitoring':
                await this.stopMonitoring();
                break;
            case 'open_demo':
                await this.openDemoPage(message.demo);
                break;
        }
    }

    async startMonitoring() {
        if (this.isMonitoring) return;
        
        console.log('🔍 Starting postMessage monitoring...');
        this.isMonitoring = true;

        // Monitor existing pages
        const pages = await this.browser.pages();
        for (const page of pages) {
            await this.attachToPage(page);
        }

        // Monitor new pages
        this.browser.on('targetcreated', async (target) => {
            if (target.type() === 'page') {
                const page = await target.page();
                await this.attachToPage(page);
            }
        });

        this.broadcastToClients({
            type: 'monitoring_started',
            data: { timestamp: Date.now() }
        });
    }

    async stopMonitoring() {
        if (!this.isMonitoring) return;
        
        console.log('🛑 Stopping postMessage monitoring...');
        this.isMonitoring = false;

        // Remove listeners from all pages
        for (const [id, page] of this.pages) {
            try {
                await page.removeAllListeners();
            } catch (error) {
                console.error(`Error removing listeners from page ${id}:`, error);
            }
        }

        this.broadcastToClients({
            type: 'monitoring_stopped',
            data: { timestamp: Date.now() }
        });
    }

    async attachToPage(page) {
        const pageId = page.url() + '_' + Date.now();
        this.pages.set(pageId, page);

        console.log(`📄 Attached to page: ${page.url()}`);

        // Inject monitoring script
        await page.evaluateOnNewDocument(() => {
            // Override postMessage to capture all calls
            const originalPostMessage = window.postMessage;
            window.postMessage = function(message, targetOrigin, transfer) {
                // Log the outgoing message
                console.log('📤 PostMessage SEND:', {
                    message,
                    targetOrigin,
                    origin: window.location.origin,
                    timestamp: Date.now()
                });
                
                // Call original function
                return originalPostMessage.call(this, message, targetOrigin, transfer);
            };

            // Monitor incoming messages
            window.addEventListener('message', (event) => {
                console.log('📥 PostMessage RECEIVE:', {
                    data: event.data,
                    origin: event.origin,
                    source: event.source?.location?.href || 'unknown',
                    timestamp: Date.now()
                });
            }, true);
        });

        // Listen for console messages to capture our logs
        page.on('console', (msg) => {
            const text = msg.text();
            if (text.includes('PostMessage')) {
                this.handlePostMessageEvent(text, pageId);
            }
        });

        // Handle page close
        page.on('close', () => {
            this.pages.delete(pageId);
            this.broadcastToClients({
                type: 'page_update',
                data: { count: this.pages.size }
            });
        });

        this.broadcastToClients({
            type: 'page_update',
            data: { count: this.pages.size }
        });
    }

    handlePostMessageEvent(logText, pageId) {
        try {
            // Parse the console log to extract postMessage data
            const match = logText.match(/📤|📥/);
            if (!match) return;

            const direction = logText.includes('📤') ? 'send' : 'receive';
            const timestamp = Date.now();

            // Extract data from log (simplified parsing)
            const eventData = {
                direction,
                pageId,
                timestamp,
                raw: logText
            };

            this.messageLog.push(eventData);

            // Broadcast to connected clients
            this.broadcastToClients({
                type: 'postmessage_event',
                data: eventData
            });

        } catch (error) {
            console.error('Error parsing postMessage event:', error);
        }
    }

    async openDemoPage(demo) {
        const urls = {
            'cross-origin-iframe': 'http://localhost:3001/cross-origin-iframe/parent.html',
            'inter-tab': 'http://localhost:3001/inter-tab/tab1.html',
            'parent-child-window': 'http://localhost:3001/parent-child-window/parent.html',
            'multi-tab-complex': 'http://localhost:3001/multi-tab-complex/tab1.html'
        };

        const url = urls[demo];
        if (!url) {
            console.error('Unknown demo:', demo);
            return;
        }

        try {
            const page = await this.browser.newPage();
            await page.goto(url);
            
            if (this.isMonitoring) {
                await this.attachToPage(page);
            }
            
            console.log(`🌐 Opened demo page: ${url}`);
        } catch (error) {
            console.error('Error opening demo page:', error);
        }
    }

    broadcastToClients(message) {
        const messageStr = JSON.stringify(message);
        this.clients.forEach(client => {
            if (client.readyState === 1) { // WebSocket.OPEN
                client.send(messageStr);
            }
        });
    }

    async shutdown() {
        console.log('🛑 Shutting down PostMessage Monitor...');
        
        if (this.browser) {
            await this.browser.close();
        }
        
        if (this.wsServer) {
            this.wsServer.close();
        }
        
        if (this.httpServer) {
            this.httpServer.close();
        }
        
        console.log('✅ PostMessage Monitor shut down');
    }
}

// Main execution
const monitor = new PostMessageMonitor();

async function main() {
    try {
        await monitor.initialize();
        
        // Handle graceful shutdown
        process.on('SIGINT', async () => {
            await monitor.shutdown();
            process.exit(0);
        });
        
        process.on('SIGTERM', async () => {
            await monitor.shutdown();
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ Failed to initialize monitor:', error);
        process.exit(1);
    }
}

main();

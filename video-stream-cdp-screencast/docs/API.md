# API Documentation

This document describes the APIs and message protocols used in the CDP Screencast POC system.

## REST API Endpoints

### Signaling Server (Port 3000)

#### GET /api/status
Returns the current status of the system.

**Response**:
```json
{
  "status": "running",
  "clients": 2,
  "browser": {
    "running": true,
    "cdpPort": 9222,
    "managerTab": true,
    "targetTabs": 4,
    "targets": ["google", "github", "stackoverflow", "wikipedia"]
  },
  "uptime": 123.45
}
```

#### POST /api/browser/launch
Launches a new Chrome browser instance with CDP enabled.

**Response**:
```json
{
  "success": true,
  "cdpPort": 9222,
  "browserWSEndpoint": "ws://localhost:9222/devtools/browser/...",
  "targets": 4
}
```

#### POST /api/browser/close
Closes the current browser instance.

**Response**:
```json
{
  "success": true
}
```

#### GET /api/targets
Returns the list of available browser targets.

**Response**:
```json
{
  "success": true,
  "targets": [
    {
      "id": "google",
      "title": "Google",
      "url": "https://google.com",
      "type": "page",
      "favicon": "https://google.com/favicon.ico"
    }
  ]
}
```

#### GET /health
Health check endpoint.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## WebSocket Protocol

### Connection URL
- **Signaling Server**: `ws://localhost:3000`

### Message Format
All WebSocket messages are JSON objects with a `type` field.

```json
{
  "type": "message_type",
  "...": "additional fields"
}
```

## Client → Server Messages

### Client Identification
```json
{
  "type": "identify",
  "clientType": "ui" | "manager",
  "userAgent": "Mozilla/5.0..."
}
```

### Request Targets
```json
{
  "type": "getTargets"
}
```

### Select Target
```json
{
  "type": "selectTarget",
  "targetId": "google"
}
```

### WebRTC Signaling
```json
{
  "type": "webrtc-offer",
  "offer": {
    "type": "offer",
    "sdp": "v=0\r\no=..."
  },
  "targetClientId": "client-123"
}
```

```json
{
  "type": "webrtc-answer",
  "answer": {
    "type": "answer",
    "sdp": "v=0\r\no=..."
  },
  "fromClientId": "client-123"
}
```

```json
{
  "type": "webrtc-ice-candidate",
  "candidate": {
    "candidate": "candidate:1 1 UDP 2130706431 ************* 54400 typ host",
    "sdpMLineIndex": 0,
    "sdpMid": "0"
  },
  "fromClientId": "client-123"
}
```

## Server → Client Messages

### Welcome Message
```json
{
  "type": "welcome",
  "clientId": "client-123",
  "serverTime": "2024-01-01T12:00:00.000Z"
}
```

### Available Targets
```json
{
  "type": "targets",
  "targets": [
    {
      "id": "google",
      "title": "Google",
      "url": "https://google.com",
      "type": "page",
      "favicon": "https://google.com/favicon.ico"
    }
  ]
}
```

### Manager Status
```json
{
  "type": "managerReady",
  "capabilities": {
    "screencast": true,
    "webrtc": true,
    "targets": 4
  }
}
```

```json
{
  "type": "managerDisconnected"
}
```

### Error Messages
```json
{
  "type": "error",
  "message": "Error description",
  "code": "ERROR_CODE"
}
```

## WebRTC Data Channel Protocol

### Channel Name
- **Screencast Data**: `screencast`

### Message Format
All data channel messages are JSON strings.

## Client → Manager Messages

### Quality Control
```json
{
  "type": "qualityRequest",
  "quality": 80,
  "resolution": {
    "width": 1280,
    "height": 720
  }
}
```

### Connection Monitoring
```json
{
  "type": "ping",
  "timestamp": 1704110400000
}
```

## Manager → Client Messages

### Screencast Frame
```json
{
  "type": "screencastFrame",
  "data": "base64-encoded-jpeg-data",
  "metadata": {
    "frameNumber": 123,
    "timestamp": 1704110400000,
    "frameRate": 30.0,
    "targetId": "google"
  }
}
```

### Connection Response
```json
{
  "type": "pong",
  "timestamp": 1704110400000
}
```

## Chrome DevTools Protocol (CDP)

### Connection
- **WebSocket URL**: `ws://localhost:9222/devtools/browser/...`

### Key Methods Used

#### Target.getTargets
Discover available browser targets.

**Request**:
```json
{
  "id": 1,
  "method": "Target.getTargets",
  "params": {}
}
```

**Response**:
```json
{
  "id": 1,
  "result": {
    "targetInfos": [
      {
        "targetId": "page-1",
        "type": "page",
        "title": "Google",
        "url": "https://google.com",
        "attached": false,
        "canAccessOpener": false
      }
    ]
  }
}
```

#### Target.attachToTarget
Attach to a specific target for control.

**Request**:
```json
{
  "id": 2,
  "method": "Target.attachToTarget",
  "params": {
    "targetId": "page-1",
    "flatten": true
  }
}
```

**Response**:
```json
{
  "id": 2,
  "result": {
    "sessionId": "session-123"
  }
}
```

#### Page.startScreencast
Begin screencast capture.

**Request**:
```json
{
  "id": 3,
  "method": "Page.startScreencast",
  "params": {
    "format": "jpeg",
    "quality": 80,
    "maxWidth": 1280,
    "maxHeight": 720,
    "everyNthFrame": 1
  },
  "sessionId": "session-123"
}
```

#### Page.screencastFrame (Event)
Screencast frame data.

**Event**:
```json
{
  "method": "Page.screencastFrame",
  "params": {
    "data": "base64-encoded-jpeg",
    "metadata": {
      "offsetTop": 0,
      "pageScaleFactor": 1,
      "deviceWidth": 1280,
      "deviceHeight": 720,
      "scrollOffsetX": 0,
      "scrollOffsetY": 0,
      "timestamp": 1704110400.123
    },
    "sessionId": 456
  }
}
```

#### Page.screencastFrameAck
Acknowledge received frame.

**Request**:
```json
{
  "id": 4,
  "method": "Page.screencastFrameAck",
  "params": {
    "sessionId": 456
  },
  "sessionId": "session-123"
}
```

## Error Codes

### WebSocket Errors
- `CONNECTION_FAILED`: Failed to establish WebSocket connection
- `INVALID_MESSAGE`: Malformed JSON message
- `UNAUTHORIZED`: Client not properly identified
- `MANAGER_NOT_READY`: CDP manager is not available

### Browser Management Errors
- `BROWSER_LAUNCH_FAILED`: Failed to launch Chrome browser
- `CDP_CONNECTION_FAILED`: Failed to connect to Chrome DevTools Protocol
- `TARGET_NOT_FOUND`: Requested target does not exist
- `SCREENCAST_FAILED`: Failed to start screencast

### WebRTC Errors
- `PEER_CONNECTION_FAILED`: Failed to establish WebRTC connection
- `DATA_CHANNEL_FAILED`: Failed to create or use data channel
- `ICE_CONNECTION_FAILED`: ICE connection establishment failed

## Rate Limiting

### WebSocket Messages
- Maximum 100 messages per second per client
- Burst limit: 200 messages

### API Endpoints
- Maximum 60 requests per minute per IP
- Browser launch: Maximum 5 requests per minute

## Security Considerations

### Development Mode
- No authentication required
- CORS disabled
- All origins allowed

### Production Recommendations
- Implement JWT-based authentication
- Enable CORS with specific origins
- Use WSS (WebSocket Secure)
- Add rate limiting
- Validate all input parameters

## Examples

### Complete Client Connection Flow

1. **Connect to WebSocket**:
   ```javascript
   const ws = new WebSocket('ws://localhost:3000');
   ```

2. **Identify as UI client**:
   ```javascript
   ws.send(JSON.stringify({
     type: 'identify',
     clientType: 'ui',
     userAgent: navigator.userAgent
   }));
   ```

3. **Request targets**:
   ```javascript
   ws.send(JSON.stringify({
     type: 'getTargets'
   }));
   ```

4. **Select target**:
   ```javascript
   ws.send(JSON.stringify({
     type: 'selectTarget',
     targetId: 'google'
   }));
   ```

5. **Handle WebRTC offer**:
   ```javascript
   ws.onmessage = async (event) => {
     const message = JSON.parse(event.data);
     if (message.type === 'webrtc-offer') {
       // Handle WebRTC offer and send answer
     }
   };
   ```

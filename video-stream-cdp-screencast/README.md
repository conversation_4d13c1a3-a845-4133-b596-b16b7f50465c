# CDP Screencast Video Streaming POC

A proof-of-concept system that streams browser content to end users using Chrome DevTools Protocol (CDP) screencast functionality with WebRTC.

## 🎯 Overview

This system demonstrates real-time browser content streaming using:
- Chrome DevTools Protocol (CDP) for screencast capture
- WebRTC for peer-to-peer video streaming
- WebSocket signaling for connection orchestration
- Multi-tab browser management and target switching

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Signaling      │    │  Chrome Browser  │    │  Client UI      │
│  Server         │◄──►│  + CDP Manager   │◄──►│  (WebRTC)       │
│  (WebSocket)    │    │  Tab             │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Browser        │    │  Target Tabs:    │    │  Video Stream   │
│  Management     │    │  • google.com    │    │  Display        │
│  & WebRTC       │    │  • github.com    │    │  & Controls     │
│  Orchestration  │    │  • stackoverflow │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Chrome/Chromium browser
- Git

### Installation
```bash
cd video-stream-cdp-screencast
npm install
npm run setup
```

### Running the System
```bash
npm start
```

This will:
1. Start the signaling server
2. Launch Chrome with CDP enabled
3. Open multiple target tabs
4. Inject the CDP controller script
5. Start the client UI

### Manual Setup
```bash
# Terminal 1: Start signaling server
npm run start:server

# Terminal 2: Start client UI
npm run start:client

# Then manually open Chrome with:
# chrome --remote-debugging-port=9222 --disable-web-security
```

## 📁 Project Structure

```
video-stream-cdp-screencast/
├── signaling-server/          # WebSocket + WebRTC orchestration
│   ├── server.js             # Main server implementation
│   ├── browser-manager.js    # Chrome browser management
│   ├── webrtc-signaling.js   # WebRTC signaling handler
│   └── package.json          # Server dependencies
├── cdp-controller/            # Browser-side CDP manager
│   ├── manager.js            # CDP controller script
│   ├── screencast.js         # Screencast functionality
│   └── webrtc-client.js      # WebRTC peer connection
├── client-ui/                 # Web application UI
│   ├── index.html            # Main UI interface
│   ├── app.js                # Client application logic
│   ├── webrtc-handler.js     # WebRTC client implementation
│   └── style.css             # UI styling
├── scripts/                   # Setup and utility scripts
│   ├── start.js              # Main startup script
│   ├── setup.js              # Environment setup
│   └── test.js               # Testing utilities
├── docs/                      # Documentation
│   ├── ARCHITECTURE.md       # Detailed architecture
│   ├── API.md                # API documentation
│   └── TROUBLESHOOTING.md    # Common issues
└── package.json               # Main project configuration
```

## 🔧 Configuration

### Chrome Launch Options
- `--remote-debugging-port=9222`: Enable CDP
- `--disable-web-security`: Allow cross-origin requests
- `--disable-features=VizDisplayCompositor`: Improve screencast performance

### WebRTC Configuration
- STUN servers for NAT traversal
- Configurable video quality settings
- Data channel for control messages

### Target Websites
Default targets include:
- google.com
- github.com
- stackoverflow.com
- Custom URLs can be configured

## 📊 Features

### Core Functionality
- ✅ CDP-based screencast capture
- ✅ WebRTC peer-to-peer streaming
- ✅ Multi-tab target management
- ✅ Real-time target switching
- ✅ Responsive client UI

### Advanced Features
- ✅ Error handling and reconnection
- ✅ Stream quality controls
- ✅ Connection status monitoring
- ✅ Debug information display
- ✅ Graceful resource cleanup

## 🐛 Troubleshooting

### Common Issues
1. **Chrome won't start with CDP**: Check port 9222 availability
2. **WebRTC connection fails**: Verify STUN/TURN server configuration
3. **No video stream**: Ensure target tabs are fully loaded
4. **Permission denied**: Grant screen capture permissions

### Debug Mode
Enable debug logging by setting `DEBUG=true` in environment variables.

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

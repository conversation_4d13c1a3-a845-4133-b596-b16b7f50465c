<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complex Multi-Tab - Child Window</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 15px;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            min-height: calc(100vh - 30px);
            font-size: 14px;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 15px;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .status-card {
            background: rgba(102, 126, 234, 0.1);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid rgba(102, 126, 234, 0.2);
            text-align: center;
        }
        .status-card h4 {
            margin: 0 0 5px 0;
            color: #667eea;
            font-size: 12px;
        }
        .status-card div {
            font-size: 11px;
        }
        .controls {
            display: flex;
            gap: 8px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            flex: 1;
            min-width: 150px;
        }
        button {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-secondary {
            background: #fd79a8;
            color: white;
        }
        .btn-success {
            background: #00b894;
            color: white;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .log {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            white-space: pre-wrap;
        }
        .connection-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        .info-item {
            margin: 5px 0;
            font-size: 12px;
        }
        .connection-status {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            margin: 10px 0;
            font-size: 12px;
        }
        .connection-status.connected {
            background: rgba(0, 184, 148, 0.2);
            color: #00b894;
            border: 1px solid rgba(0, 184, 148, 0.3);
        }
        .connection-status.disconnected {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        .message-display {
            background: rgba(253, 121, 168, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid rgba(253, 121, 168, 0.2);
            min-height: 40px;
        }
        .message-display h4 {
            margin: 0 0 8px 0;
            color: #fd79a8;
            font-size: 12px;
        }
        .message-content {
            font-size: 11px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎈 Complex Child Window</h1>
            <p>Opened by Tab 2, can receive messages from Tab 1</p>
            <p><strong>Child ID:</strong> <span id="childId"></span></p>
        </div>

        <div id="connectionStatus" class="connection-status disconnected">
            ❌ Not connected to parent
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h4>📡 Communication</h4>
                <div id="communicationStatus">Initializing...</div>
            </div>
            <div class="status-card">
                <h4>📊 Messages</h4>
                <div id="messageStats">Received: 0</div>
            </div>
            <div class="status-card">
                <h4>🪟 Window</h4>
                <div id="windowInfo">Loading...</div>
            </div>
        </div>

        <div class="connection-info">
            <h4>👨‍👩‍👧‍👦 Parent Information</h4>
            <div class="info-item"><strong>Parent ID:</strong> <span id="parentId">Not detected</span></div>
            <div class="info-item"><strong>Parent Role:</strong> <span id="parentRole">Unknown</span></div>
            <div class="info-item"><strong>Connection Time:</strong> <span id="connectionTime">Not connected</span></div>
        </div>

        <div class="message-display">
            <h4>📨 Latest Message</h4>
            <div id="latestMessage" class="message-content">No messages received yet</div>
        </div>

        <div class="container">
            <h3>📤 Send Response</h3>
            <div class="controls">
                <input type="text" id="responseInput" placeholder="Enter response..." value="Hello from child!">
                <button class="btn-primary" onclick="sendResponse()">Send Response</button>
            </div>
            
            <div class="controls">
                <button class="btn-secondary" onclick="sendStatus()">Send Status</button>
                <button class="btn-success" onclick="sendHeartbeat()">Send Heartbeat</button>
            </div>
        </div>

        <div class="container">
            <h3>📋 Communication Log</h3>
            <div id="messageLog" class="log"></div>
            <button onclick="clearLog()" style="margin-top: 8px; padding: 6px 12px; background: #667eea; color: white; border: none; border-radius: 4px; font-size: 11px;">Clear Log</button>
        </div>
    </div>

    <script>
        // Child window identification
        const CHILD_ID = 'child_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // State management
        let parentWindow = null;
        let parentOrigin = null;
        let parentId = null;
        let parentRole = null;
        let connectionTime = null;
        let messageCounter = 0;
        let receivedMessages = 0;
        let isConnected = false;
        
        // DOM elements
        const responseInput = document.getElementById('responseInput');
        const messageLog = document.getElementById('messageLog');
        const latestMessage = document.getElementById('latestMessage');
        
        // Initialize
        document.getElementById('childId').textContent = CHILD_ID;
        updateWindowInfo();
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            console.log(`Child: ${message}`);
        }
        
        function updateStats() {
            document.getElementById('messageStats').textContent = `Received: ${receivedMessages}`;
        }
        
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                statusElement.textContent = '✅ Connected to parent';
                document.getElementById('communicationStatus').textContent = 'Connected';
            } else {
                statusElement.className = 'connection-status disconnected';
                statusElement.textContent = '❌ Not connected to parent';
                document.getElementById('communicationStatus').textContent = 'Waiting...';
            }
        }
        
        function updateWindowInfo() {
            document.getElementById('windowInfo').textContent = `${window.innerWidth}x${window.innerHeight}`;
        }
        
        function updateParentInfo() {
            document.getElementById('parentId').textContent = parentId || 'Not detected';
            document.getElementById('parentRole').textContent = parentRole || 'Unknown';
            document.getElementById('connectionTime').textContent = connectionTime ? 
                new Date(connectionTime).toLocaleTimeString() : 'Not connected';
        }
        
        function updateLatestMessage(message, sender) {
            latestMessage.innerHTML = `<strong>From ${sender}:</strong> ${message}`;
        }
        
        // Listen for messages from parent and network
        window.addEventListener('message', function(event) {
            // Store parent information on first message
            if (!parentWindow) {
                parentWindow = event.source;
                parentOrigin = event.origin;
                connectionTime = Date.now();
                updateConnectionStatus(true);
                updateParentInfo();
                log(`✅ Connected to parent: ${parentOrigin}`, 'success');
            }
            
            // Security check: validate origin
            if (event.origin !== parentOrigin) {
                log(`❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`, 'security');
                return;
            }
            
            // Validate message structure
            if (!event.data || typeof event.data !== 'object') {
                log(`❌ SECURITY: Invalid message format received`, 'security');
                return;
            }
            
            const { type, payload, parentId: msgParentId, senderId, senderName, senderRole, messageId } = event.data;
            
            // Store parent information
            if (msgParentId && !parentId) {
                parentId = msgParentId;
                parentRole = 'window_manager';
                updateParentInfo();
            }
            
            // Store sender information if different from parent
            if (senderId && senderId !== parentId && senderRole) {
                parentRole = senderRole;
                updateParentInfo();
            }
            
            log(`📥 Received: ${type} (ID: ${messageId})`, 'receive');
            receivedMessages++;
            updateStats();
            
            // Handle different message types
            switch (type) {
                case 'direct_message':
                    log(`💬 Direct message: "${payload.message}"`, 'message');
                    updateLatestMessage(payload.message, 'Parent');
                    break;
                    
                case 'broadcast_message':
                    log(`📢 Broadcast: "${payload.message}"`, 'message');
                    updateLatestMessage(payload.message, 'Parent (Broadcast)');
                    break;
                    
                case 'relayed_message':
                    log(`🔄 Relayed message from ${payload.originalSender}: "${payload.originalMessage}"`, 'relay');
                    updateLatestMessage(payload.originalMessage, `${payload.originalSender} (via ${payload.relayedBy})`);
                    break;
                    
                case 'data_packet_relay':
                    log(`📦 Data packet from ${payload.originalSender}:`, 'data');
                    log(`  Sequence: ${payload.originalData.sequence}`, 'data');
                    log(`  Payload: ${payload.originalData.payload}`, 'data');
                    updateLatestMessage(`Data packet: ${payload.originalData.payload}`, `${payload.originalSender} (via ${payload.relayedBy})`);
                    break;
                    
                case 'ping_request':
                    log(`🏓 Ping from network`, 'ping');
                    sendPong(payload.originalTimestamp);
                    break;
                    
                case 'emergency_signal':
                    log(`🚨 Emergency signal: ${payload.message}`, 'emergency');
                    updateLatestMessage(`EMERGENCY: ${payload.message}`, 'Network');
                    sendEmergencyAck();
                    break;
                    
                case 'shutdown':
                    log(`🛑 Shutdown signal received`, 'shutdown');
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                    break;
                    
                default:
                    log(`⚠️ Unknown message type: ${type}`, 'warning');
            }
        });
        
        function sendToParent(data) {
            if (!parentWindow || !parentOrigin) {
                log('❌ Cannot send message: parent not connected', 'error');
                return false;
            }
            
            try {
                parentWindow.postMessage(data, parentOrigin);
                return true;
            } catch (error) {
                log(`❌ Failed to send message: ${error.message}`, 'error');
                return false;
            }
        }
        
        function sendResponse() {
            const response = responseInput.value.trim();
            if (!response) {
                log('❌ Cannot send empty response', 'error');
                return;
            }
            
            const responseData = {
                type: 'child_response',
                payload: { message: response },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(responseData)) {
                log(`📤 Sent response: "${response}"`, 'send');
                responseInput.value = '';
            }
        }
        
        function sendStatus() {
            const statusData = {
                type: 'child_status',
                payload: {
                    status: 'active',
                    windowSize: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    position: {
                        x: window.screenX,
                        y: window.screenY
                    },
                    messagesReceived: receivedMessages,
                    uptime: connectionTime ? Date.now() - connectionTime : 0
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(statusData)) {
                log('📊 Sent status update', 'send');
            }
        }
        
        function sendHeartbeat() {
            const heartbeatData = {
                type: 'child_heartbeat',
                payload: { timestamp: Date.now() },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(heartbeatData)) {
                log('💓 Sent heartbeat', 'send');
            }
        }
        
        function sendPong(originalTimestamp) {
            const pongData = {
                type: 'child_pong',
                payload: { 
                    originalTimestamp: originalTimestamp,
                    pongTimestamp: Date.now()
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(pongData)) {
                log('🏓 Sent pong response', 'send');
            }
        }
        
        function sendEmergencyAck() {
            const ackData = {
                type: 'emergency_ack',
                payload: { 
                    status: 'acknowledged',
                    childStatus: 'safe'
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(ackData)) {
                log('🚨 Sent emergency acknowledgment', 'send');
            }
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Handle Enter key in response input
        responseInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendResponse();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', updateWindowInfo);
        
        // Send ready signal when loaded
        window.addEventListener('load', function() {
            log('🚀 Child window loaded', 'success');
            
            // Wait a bit for parent to be ready
            setTimeout(() => {
                const readyData = {
                    type: 'child_ready',
                    payload: {
                        url: window.location.href,
                        timestamp: new Date().toISOString(),
                        capabilities: ['messaging', 'status_reporting', 'emergency_response']
                    },
                    childId: CHILD_ID,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                // Try to send to parent (might not be connected yet)
                if (window.opener) {
                    try {
                        window.opener.postMessage(readyData, window.location.origin);
                        log('📤 Sent ready signal to parent', 'send');
                    } catch (error) {
                        log('⚠️ Could not send ready signal (parent not ready yet)', 'warning');
                    }
                }
            }, 100);
        });
        
        // Handle window closing
        window.addEventListener('beforeunload', function() {
            if (parentWindow) {
                const closingData = {
                    type: 'child_closing',
                    payload: { reason: 'Window closing' },
                    childId: CHILD_ID,
                    timestamp: Date.now()
                };
                
                try {
                    parentWindow.postMessage(closingData, parentOrigin);
                } catch (error) {
                    // Ignore errors during closing
                }
            }
        });
        
        // Send periodic heartbeats
        setInterval(() => {
            if (isConnected) {
                sendHeartbeat();
            }
        }, 15000); // Every 15 seconds
        
        // Initial log entry
        log('🎈 Child window initialized', 'success');
        log(`Child ID: ${CHILD_ID}`, 'info');
        log('Waiting for parent connection...', 'info');
    </script>
</body>
</html>

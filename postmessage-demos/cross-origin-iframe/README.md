# Cross-Origin Iframe Communication Example

This example demonstrates secure cross-origin communication between a parent page and an iframe from a different origin using the `window.postMessage` API.

## 🎯 What This Example Demonstrates

- **Cross-origin messaging** between parent window and iframe
- **Origin validation** for security
- **Message structure validation** to prevent malicious data
- **Bidirectional communication** (parent ↔ iframe)
- **Error handling** and comprehensive logging
- **Different message types** (echo, data updates, info requests)

## 🏗️ Architecture

```
Parent Page (localhost:3001)
├── Loads iframe from different origin
├── Sends various types of messages
├── Validates incoming messages
└── Logs all communication

Iframe (localhost:3002)
├── Receives messages from parent
├── Validates parent origin
├── Processes different message types
└── Sends responses back to parent
```

## 🚀 How to Test

### 1. Start the Servers
```bash
# From the project root
npm run start-servers
```

### 2. Open the Parent Page
Navigate to: `http://localhost:3001/cross-origin-iframe/parent.html`

### 3. Test Communication
1. **Wait for iframe to load** - You'll see "✅ Iframe loaded and ready for communication"
2. **Send a simple message** - Type in the input field and click "Send Message"
3. **Send complex data** - Click "Send Data Object" to send structured data
4. **Request iframe info** - Click "Request Iframe Info" to get iframe details
5. **Test iframe-initiated messages** - Use buttons in the iframe to send messages to parent

## 🔒 Security Features

### Origin Validation
```javascript
// Parent validates iframe origin
const ALLOWED_ORIGINS = ['http://localhost:3002'];
if (!ALLOWED_ORIGINS.includes(event.origin)) {
    // Reject message
    return;
}

// Iframe validates parent origin
const ALLOWED_ORIGINS = ['http://localhost:3001'];
if (!ALLOWED_ORIGINS.includes(event.origin)) {
    // Reject message
    return;
}
```

### Message Structure Validation
```javascript
// Validate message format
if (!event.data || typeof event.data !== 'object') {
    log('Invalid message format received', 'security');
    return;
}

const { type, payload, messageId } = event.data;
// Process only known message types
```

### Content Security Policy
The servers set appropriate headers:
- `X-Frame-Options: SAMEORIGIN` (parent) / `ALLOW-FROM http://localhost:3001` (iframe)
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`

## 📨 Message Types

### Parent → Iframe
- `echo_request` - Simple echo message
- `data_update` - Complex data object
- `info_request` - Request iframe information

### Iframe → Parent
- `iframe_ready` - Iframe loaded and ready
- `echo_response` - Response to echo request
- `iframe_info` - Iframe information
- `greeting` - Simple greeting message
- `random_data` - Random data for testing
- `error` - Error messages

## 🐛 Debugging Features

### Console Logging
Both parent and iframe log all messages to the console with:
- Timestamp
- Message type and direction
- Origin validation results
- Payload content

### Visual Logging
Both pages include visual log areas showing:
- Message flow
- Security validations
- Error conditions
- Connection status

### Message Tracking
Each message includes:
- Unique message ID
- Timestamp
- Message type
- Payload data

## ⚠️ Common Issues

### 1. Iframe Not Loading
- Check that both servers are running
- Verify the iframe src URL is correct
- Check browser console for CORS errors

### 2. Messages Not Received
- Verify origin validation arrays include correct URLs
- Check that message structure includes required fields
- Ensure target origin is specified correctly in postMessage calls

### 3. Security Errors
- Browser may block cross-origin iframe loading
- Check X-Frame-Options headers
- Verify CORS configuration

## 🔧 Customization

### Adding New Message Types
1. Add handler in iframe:
```javascript
case 'new_message_type':
    handleNewMessageType(payload, messageId, event.origin);
    break;
```

2. Add sender function in parent:
```javascript
function sendNewMessage() {
    const messageData = {
        type: 'new_message_type',
        payload: { /* your data */ },
        messageId: ++messageCounter,
        timestamp: Date.now()
    };
    iframe.contentWindow.postMessage(messageData, IFRAME_ORIGIN);
}
```

### Changing Origins
Update the `ALLOWED_ORIGINS` arrays in both files to match your domains.

## 📚 Learning Points

1. **Always validate origins** before processing messages
2. **Structure your messages** with consistent format (type, payload, messageId)
3. **Handle errors gracefully** and provide meaningful feedback
4. **Log everything** for debugging purposes
5. **Use unique message IDs** to track request/response pairs
6. **Implement timeouts** for request/response patterns (not shown in this basic example)

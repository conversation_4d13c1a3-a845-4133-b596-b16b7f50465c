<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent-Child Window Communication - Child</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #333;
            min-height: calc(100vh - 40px);
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #667eea;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin: 15px 0;
            align-items: center;
        }
        .control-row {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        input[type="text"] {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            flex: 1;
            min-width: 200px;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-secondary {
            background: #f093fb;
            color: white;
        }
        .btn-danger {
            background: #ff6b6b;
            color: white;
        }
        .btn-success {
            background: #51cf66;
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .log {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            height: 250px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .parent-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        .info-item {
            margin: 8px 0;
            font-size: 14px;
        }
        .connection-status {
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            margin: 15px 0;
        }
        .connection-status.connected {
            background: rgba(81, 207, 102, 0.2);
            color: #51cf66;
            border: 1px solid rgba(81, 207, 102, 0.3);
        }
        .connection-status.disconnected {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        .special-features {
            background: rgba(240, 147, 251, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid rgba(240, 147, 251, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎈 Child Window</h1>
            <p>This child window communicates with its parent</p>
            <p><strong>Child ID:</strong> <span id="childId"></span></p>
        </div>

        <div id="connectionStatus" class="connection-status disconnected">
            ❌ Not connected to parent
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h4>📡 Communication</h4>
                <div id="communicationStatus">Initializing...</div>
            </div>
            <div class="status-card">
                <h4>📊 Messages</h4>
                <div id="messageStats">Sent: 0, Received: 0</div>
            </div>
            <div class="status-card">
                <h4>🪟 Window Info</h4>
                <div id="windowInfo">Loading...</div>
            </div>
        </div>

        <div class="parent-info">
            <h4>👨‍👩‍👧‍👦 Parent Information</h4>
            <div class="info-item"><strong>Parent ID:</strong> <span id="parentId">Not detected</span></div>
            <div class="info-item"><strong>Parent URL:</strong> <span id="parentUrl">Not detected</span></div>
            <div class="info-item"><strong>Connection Time:</strong> <span id="connectionTime">Not connected</span></div>
            <div class="info-item"><strong>Last Message:</strong> <span id="lastMessage">None</span></div>
        </div>

        <div class="container">
            <h3>📤 Send Messages to Parent</h3>
            <div class="controls">
                <input type="text" id="messageInput" placeholder="Enter message to send to parent..." value="Hello from child!">
                <button class="btn-primary" onclick="sendMessageToParent()">Send Message</button>
            </div>
            
            <div class="control-row">
                <button class="btn-secondary" onclick="sendChildInfo()">Send Child Info</button>
                <button class="btn-secondary" onclick="requestParentInfo()">Request Parent Info</button>
                <button class="btn-success" onclick="sendHeartbeat()">Send Heartbeat</button>
            </div>
        </div>

        <div class="container special-features">
            <h3>🎯 Special Child Features</h3>
            <div class="control-row">
                <button class="btn-secondary" onclick="sendWindowState()">Send Window State</button>
                <button class="btn-secondary" onclick="simulateWork()">Simulate Work</button>
                <button class="btn-danger" onclick="sendErrorReport()">Send Error Report</button>
            </div>
            <div class="control-row">
                <button class="btn-success" onclick="resizeWindow()">Resize Window</button>
                <button class="btn-danger" onclick="closeWindow()">Close Window</button>
            </div>
        </div>

        <div class="container">
            <h3>📋 Communication Log</h3>
            <div id="messageLog" class="log"></div>
            <button onclick="clearLog()" style="margin-top: 10px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px;">Clear Log</button>
        </div>
    </div>

    <script>
        // Child window identification
        const CHILD_ID = 'child_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // State management
        let parentWindow = null;
        let parentOrigin = null;
        let parentId = null;
        let connectionTime = null;
        let messageCounter = 0;
        let sentMessages = 0;
        let receivedMessages = 0;
        let isConnected = false;
        
        // DOM elements
        const messageInput = document.getElementById('messageInput');
        const messageLog = document.getElementById('messageLog');
        
        // Initialize
        document.getElementById('childId').textContent = CHILD_ID;
        updateWindowInfo();
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            messageLog.textContent += logEntry;
            messageLog.scrollTop = messageLog.scrollHeight;
            console.log(`Child: ${message}`);
        }
        
        function updateStats() {
            document.getElementById('messageStats').textContent = `Sent: ${sentMessages}, Received: ${receivedMessages}`;
        }
        
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            
            if (connected) {
                statusElement.className = 'connection-status connected';
                statusElement.textContent = '✅ Connected to parent';
                document.getElementById('communicationStatus').textContent = 'Connected and ready';
            } else {
                statusElement.className = 'connection-status disconnected';
                statusElement.textContent = '❌ Not connected to parent';
                document.getElementById('communicationStatus').textContent = 'Waiting for parent...';
            }
        }
        
        function updateWindowInfo() {
            document.getElementById('windowInfo').textContent = `${window.innerWidth}x${window.innerHeight}`;
        }
        
        function updateParentInfo() {
            document.getElementById('parentId').textContent = parentId || 'Not detected';
            document.getElementById('parentUrl').textContent = parentOrigin || 'Not detected';
            document.getElementById('connectionTime').textContent = connectionTime ? 
                new Date(connectionTime).toLocaleTimeString() : 'Not connected';
        }
        
        // Listen for messages from parent
        window.addEventListener('message', function(event) {
            // Store parent information on first message
            if (!parentWindow) {
                parentWindow = event.source;
                parentOrigin = event.origin;
                connectionTime = Date.now();
                updateConnectionStatus(true);
                updateParentInfo();
                log(`✅ Connected to parent: ${parentOrigin}`, 'success');
            }
            
            // Security check: validate origin
            if (event.origin !== parentOrigin) {
                log(`❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`, 'security');
                return;
            }
            
            // Validate message structure
            if (!event.data || typeof event.data !== 'object') {
                log(`❌ SECURITY: Invalid message format received`, 'security');
                return;
            }
            
            const { type, payload, parentId: msgParentId, messageId } = event.data;
            
            // Store parent ID
            if (msgParentId && !parentId) {
                parentId = msgParentId;
                updateParentInfo();
            }
            
            log(`📥 Received from parent: ${type} (ID: ${messageId})`, 'receive');
            receivedMessages++;
            updateStats();
            
            document.getElementById('lastMessage').textContent = `${type} at ${new Date().toLocaleTimeString()}`;
            
            // Handle different message types
            switch (type) {
                case 'broadcast_message':
                    log(`📢 Broadcast: "${payload.message}"`, 'message');
                    break;
                    
                case 'direct_message':
                    log(`💬 Direct message: "${payload.message}"`, 'message');
                    break;
                    
                case 'info_request':
                    log(`📋 Parent requested info`, 'request');
                    sendChildInfo();
                    break;
                    
                case 'data_update':
                    log(`📊 Data update received:`, 'data');
                    log(`  Config: ${JSON.stringify(payload.config)}`, 'data');
                    log(`  User: ${payload.user.name} (${payload.user.role})`, 'data');
                    break;
                    
                case 'ping':
                    log(`🏓 Ping received`, 'ping');
                    sendPong(payload.timestamp);
                    break;
                    
                case 'shutdown':
                    log(`🛑 Shutdown signal: ${payload.reason}`, 'shutdown');
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                    break;
                    
                case 'parent_closing':
                    log(`👋 Parent is closing: ${payload.reason}`, 'info');
                    break;
                    
                default:
                    log(`⚠️ Unknown message type: ${type}`, 'warning');
            }
        });
        
        function sendToParent(data) {
            if (!parentWindow || !parentOrigin) {
                log('❌ Cannot send message: parent not connected', 'error');
                return false;
            }
            
            try {
                parentWindow.postMessage(data, parentOrigin);
                sentMessages++;
                updateStats();
                return true;
            } catch (error) {
                log(`❌ Failed to send message: ${error.message}`, 'error');
                return false;
            }
        }
        
        function sendMessageToParent() {
            const message = messageInput.value.trim();
            if (!message) {
                log('❌ Cannot send empty message', 'error');
                return;
            }
            
            const messageData = {
                type: 'child_message',
                payload: { message: message },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(messageData)) {
                log(`📤 Sent message: "${message}"`, 'send');
                messageInput.value = '';
            }
        }
        
        function sendChildInfo() {
            const infoData = {
                type: 'child_info',
                payload: {
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    screen: {
                        width: screen.width,
                        height: screen.height
                    },
                    timestamp: new Date().toISOString()
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(infoData)) {
                log('📊 Sent child info to parent', 'send');
            }
        }
        
        function requestParentInfo() {
            const requestData = {
                type: 'parent_info_request',
                payload: {},
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(requestData)) {
                log('📋 Requested parent info', 'send');
            }
        }
        
        function sendHeartbeat() {
            const heartbeatData = {
                type: 'heartbeat',
                payload: { timestamp: Date.now() },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(heartbeatData)) {
                log('💓 Sent heartbeat to parent', 'send');
            }
        }
        
        function sendPong(originalTimestamp) {
            const pongData = {
                type: 'pong',
                payload: { 
                    originalTimestamp: originalTimestamp,
                    pongTimestamp: Date.now()
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(pongData)) {
                log('🏓 Sent pong to parent', 'send');
            }
        }
        
        function sendWindowState() {
            const stateData = {
                type: 'window_state',
                payload: {
                    position: {
                        x: window.screenX,
                        y: window.screenY
                    },
                    size: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    focused: document.hasFocus(),
                    visible: !document.hidden
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(stateData)) {
                log('🪟 Sent window state to parent', 'send');
            }
        }
        
        function simulateWork() {
            log('⚙️ Starting simulated work...', 'work');
            
            const workData = {
                type: 'work_started',
                payload: { task: 'Data processing simulation' },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            sendToParent(workData);
            
            // Simulate work progress
            let progress = 0;
            const workInterval = setInterval(() => {
                progress += 20;
                
                const progressData = {
                    type: 'work_progress',
                    payload: { 
                        task: 'Data processing simulation',
                        progress: progress,
                        status: `Processing step ${progress / 20} of 5`
                    },
                    childId: CHILD_ID,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                sendToParent(progressData);
                log(`⚙️ Work progress: ${progress}%`, 'work');
                
                if (progress >= 100) {
                    clearInterval(workInterval);
                    
                    const completeData = {
                        type: 'work_complete',
                        payload: { 
                            task: 'Data processing simulation',
                            result: 'Successfully processed 1000 records'
                        },
                        childId: CHILD_ID,
                        messageId: ++messageCounter,
                        timestamp: Date.now()
                    };
                    
                    sendToParent(completeData);
                    log('✅ Work completed', 'work');
                }
            }, 1000);
        }
        
        function sendErrorReport() {
            const errorData = {
                type: 'child_error',
                payload: { 
                    error: 'Simulated error: Network timeout',
                    code: 'NET_TIMEOUT',
                    details: 'Failed to connect to external service after 30 seconds'
                },
                childId: CHILD_ID,
                messageId: ++messageCounter,
                timestamp: Date.now()
            };
            
            if (sendToParent(errorData)) {
                log('❌ Sent error report to parent', 'send');
            }
        }
        
        function resizeWindow() {
            const newWidth = 400 + Math.random() * 400;
            const newHeight = 300 + Math.random() * 300;
            
            try {
                window.resizeTo(newWidth, newHeight);
                log(`🔄 Resized window to ${Math.round(newWidth)}x${Math.round(newHeight)}`, 'info');
                updateWindowInfo();
            } catch (error) {
                log(`❌ Failed to resize window: ${error.message}`, 'error');
            }
        }
        
        function closeWindow() {
            if (confirm('Are you sure you want to close this child window?')) {
                const closingData = {
                    type: 'child_closing',
                    payload: { reason: 'User initiated close' },
                    childId: CHILD_ID,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                sendToParent(closingData);
                log('👋 Closing window...', 'info');
                
                setTimeout(() => {
                    window.close();
                }, 500);
            }
        }
        
        function clearLog() {
            messageLog.textContent = '';
            log('📋 Log cleared', 'info');
        }
        
        // Handle Enter key in message input
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessageToParent();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', updateWindowInfo);
        
        // Send ready signal when loaded
        window.addEventListener('load', function() {
            log('🚀 Child window loaded', 'success');
            
            // Wait a bit for parent to be ready
            setTimeout(() => {
                const readyData = {
                    type: 'child_ready',
                    payload: {
                        url: window.location.href,
                        timestamp: new Date().toISOString()
                    },
                    childId: CHILD_ID,
                    messageId: ++messageCounter,
                    timestamp: Date.now()
                };
                
                // Try to send to parent (might not be connected yet)
                if (window.opener) {
                    try {
                        window.opener.postMessage(readyData, window.location.origin);
                        log('📤 Sent ready signal to parent', 'send');
                    } catch (error) {
                        log('⚠️ Could not send ready signal (parent not ready yet)', 'warning');
                    }
                }
            }, 100);
        });
        
        // Handle window closing
        window.addEventListener('beforeunload', function() {
            if (parentWindow) {
                const closingData = {
                    type: 'child_closing',
                    payload: { reason: 'Window closing' },
                    childId: CHILD_ID,
                    timestamp: Date.now()
                };
                
                try {
                    parentWindow.postMessage(closingData, parentOrigin);
                } catch (error) {
                    // Ignore errors during closing
                }
            }
        });
        
        // Initial log entry
        log('🎈 Child window initialized', 'success');
        log(`Child ID: ${CHILD_ID}`, 'info');
        log('Waiting for parent connection...', 'info');
    </script>
</body>
</html>

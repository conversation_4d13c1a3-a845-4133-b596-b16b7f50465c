// WebRTC Manager for streaming to external web clients
class WebRTCManager {
  constructor() {
    this.peerConnections = new Map();
    this.signalingSocket = null;
    this.localStream = null;
    this.isConnectedToSignaling = false;
    this.iceServers = [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ];
    this.connectionConfig = {
      iceServers: this.iceServers,
      iceCandidatePoolSize: 10
    };
  }

  async connectToSignalingServer(serverUrl) {
    try {
      if (this.signalingSocket && this.signalingSocket.readyState === WebSocket.OPEN) {
        console.log('Already connected to signaling server');
        return;
      }

      this.signalingSocket = new WebSocket(serverUrl);
      
      return new Promise((resolve, reject) => {
        this.signalingSocket.onopen = () => {
          console.log('Connected to signaling server:', serverUrl);
          this.isConnectedToSignaling = true;
          this.setupSignalingHandlers();
          resolve();
        };

        this.signalingSocket.onerror = (error) => {
          console.error('Signaling server connection error:', error);
          this.isConnectedToSignaling = false;
          reject(new Error('Failed to connect to signaling server'));
        };

        this.signalingSocket.onclose = () => {
          console.log('Signaling server connection closed');
          this.isConnectedToSignaling = false;
        };
      });
    } catch (error) {
      throw new Error(`Signaling connection failed: ${error.message}`);
    }
  }

  setupSignalingHandlers() {
    this.signalingSocket.onmessage = async (event) => {
      try {
        const message = JSON.parse(event.data);
        await this.handleSignalingMessage(message);
      } catch (error) {
        console.error('Error handling signaling message:', error);
      }
    };
  }

  async handleSignalingMessage(message) {
    const { type, clientId, data } = message;

    switch (type) {
      case 'client-connected':
        console.log('New client connected:', clientId);
        await this.createPeerConnection(clientId);
        break;

      case 'offer':
        await this.handleOffer(clientId, data);
        break;

      case 'answer':
        await this.handleAnswer(clientId, data);
        break;

      case 'ice-candidate':
        await this.handleIceCandidate(clientId, data);
        break;

      case 'client-disconnected':
        this.removePeerConnection(clientId);
        break;

      default:
        console.warn('Unknown signaling message type:', type);
    }
  }

  async createPeerConnection(clientId) {
    try {
      const peerConnection = new RTCPeerConnection(this.connectionConfig);
      
      // Add local stream if available
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          peerConnection.addTrack(track, this.localStream);
        });
      }

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendSignalingMessage({
            type: 'ice-candidate',
            clientId,
            data: event.candidate
          });
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log(`Peer connection state for ${clientId}:`, peerConnection.connectionState);
        
        if (peerConnection.connectionState === 'disconnected' || 
            peerConnection.connectionState === 'failed') {
          this.removePeerConnection(clientId);
        }
      };

      // Handle ICE connection state changes
      peerConnection.oniceconnectionstatechange = () => {
        console.log(`ICE connection state for ${clientId}:`, peerConnection.iceConnectionState);
      };

      this.peerConnections.set(clientId, peerConnection);

      // Create and send offer
      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: false,
        offerToReceiveVideo: false
      });
      
      await peerConnection.setLocalDescription(offer);
      
      this.sendSignalingMessage({
        type: 'offer',
        clientId,
        data: offer
      });

      console.log(`Created peer connection for client ${clientId}`);
      return peerConnection;
    } catch (error) {
      console.error(`Failed to create peer connection for ${clientId}:`, error);
      throw error;
    }
  }

  async handleOffer(clientId, offer) {
    try {
      let peerConnection = this.peerConnections.get(clientId);
      
      if (!peerConnection) {
        peerConnection = await this.createPeerConnection(clientId);
      }

      await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
      
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);
      
      this.sendSignalingMessage({
        type: 'answer',
        clientId,
        data: answer
      });
    } catch (error) {
      console.error(`Failed to handle offer from ${clientId}:`, error);
    }
  }

  async handleAnswer(clientId, answer) {
    try {
      const peerConnection = this.peerConnections.get(clientId);
      if (peerConnection) {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
      }
    } catch (error) {
      console.error(`Failed to handle answer from ${clientId}:`, error);
    }
  }

  async handleIceCandidate(clientId, candidate) {
    try {
      const peerConnection = this.peerConnections.get(clientId);
      if (peerConnection) {
        await peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      }
    } catch (error) {
      console.error(`Failed to handle ICE candidate from ${clientId}:`, error);
    }
  }

  sendSignalingMessage(message) {
    if (this.signalingSocket && this.signalingSocket.readyState === WebSocket.OPEN) {
      this.signalingSocket.send(JSON.stringify(message));
    } else {
      console.error('Cannot send signaling message: not connected to server');
    }
  }

  async setLocalStream(stream) {
    this.localStream = stream;
    
    // Add tracks to existing peer connections
    this.peerConnections.forEach((peerConnection, clientId) => {
      // Remove existing tracks
      const senders = peerConnection.getSenders();
      senders.forEach(sender => {
        if (sender.track) {
          peerConnection.removeTrack(sender);
        }
      });

      // Add new tracks
      stream.getTracks().forEach(track => {
        peerConnection.addTrack(track, stream);
      });
    });

    console.log('Local stream set for WebRTC streaming');
  }

  removePeerConnection(clientId) {
    const peerConnection = this.peerConnections.get(clientId);
    if (peerConnection) {
      peerConnection.close();
      this.peerConnections.delete(clientId);
      console.log(`Removed peer connection for client ${clientId}`);
    }
  }

  getConnectedClients() {
    return Array.from(this.peerConnections.keys());
  }

  getConnectionStats() {
    const stats = {
      connectedClients: this.peerConnections.size,
      signalingConnected: this.isConnectedToSignaling,
      hasLocalStream: !!this.localStream,
      clients: []
    };

    this.peerConnections.forEach((peerConnection, clientId) => {
      stats.clients.push({
        clientId,
        connectionState: peerConnection.connectionState,
        iceConnectionState: peerConnection.iceConnectionState,
        iceGatheringState: peerConnection.iceGatheringState
      });
    });

    return stats;
  }

  async getDetailedStats(clientId) {
    const peerConnection = this.peerConnections.get(clientId);
    if (!peerConnection) {
      return null;
    }

    try {
      const stats = await peerConnection.getStats();
      const result = {
        clientId,
        connectionState: peerConnection.connectionState,
        iceConnectionState: peerConnection.iceConnectionState,
        stats: {}
      };

      stats.forEach((report) => {
        result.stats[report.id] = report;
      });

      return result;
    } catch (error) {
      console.error(`Failed to get stats for ${clientId}:`, error);
      return null;
    }
  }

  disconnect() {
    // Close all peer connections
    this.peerConnections.forEach((peerConnection, clientId) => {
      this.removePeerConnection(clientId);
    });

    // Close signaling connection
    if (this.signalingSocket) {
      this.signalingSocket.close();
      this.signalingSocket = null;
      this.isConnectedToSignaling = false;
    }

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    console.log('WebRTC manager disconnected');
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebRTCManager;
} else {
  window.WebRTCManager = WebRTCManager;
}

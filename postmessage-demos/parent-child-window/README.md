# Parent-Child Window Communication Example

This example demonstrates bidirectional communication between a parent window and child popup windows using the `window.postMessage` API.

## 🎯 What This Example Demonstrates

- **Parent-child window communication** using `window.postMessage`
- **Popup window management** (opening, closing, monitoring)
- **Bidirectional messaging** with comprehensive message types
- **Window lifecycle management** (ready signals, closing notifications)
- **Multiple child window support** with individual targeting
- **Advanced features** (work simulation, window state tracking, error reporting)

## 🏗️ Architecture

```
Parent Window
├── Opens child popup windows
├── Manages multiple children
├── Sends broadcasts and direct messages
├── Monitors child window states
└── Handles child responses

Child Windows (Popups)
├── Connect to parent on load
├── Send ready signals
├── Process parent messages
├── Report status and errors
└── Notify on closing
```

## 🚀 How to Test

### 1. Start the Server
```bash
# From the project root
npm run start-servers
```

### 2. Open the Parent Window
Navigate to: `http://localhost:3001/parent-child-window/parent.html`

### 3. Test Communication
1. **Open child windows** - Click "Open Child Window" or "Open 3 Windows"
2. **Send messages** - Use the message input to broadcast to all children
3. **Individual communication** - Use "Message" button on specific child windows
4. **Test special features**:
   - Request child info
   - Send data updates
   - Ping children (latency test)
   - Send shutdown signals
5. **Child-initiated actions** - Use buttons in child windows to send various message types

## 📨 Message Types

### Parent → Child Messages
- `broadcast_message` - Message sent to all children
- `direct_message` - Message sent to specific child
- `info_request` - Request child information
- `data_update` - Send configuration/data updates
- `ping` - Latency test ping
- `shutdown` - Graceful shutdown signal
- `parent_closing` - Parent window closing notification

### Child → Parent Messages
- `child_ready` - Child window loaded and ready
- `child_info` - Child window information
- `child_message` - Simple message from child
- `heartbeat` - Keep-alive signal
- `pong` - Response to ping (latency measurement)
- `window_state` - Current window position/size
- `work_started` - Work simulation started
- `work_progress` - Work progress updates
- `work_complete` - Work completed
- `child_error` - Error report
- `child_closing` - Child window closing

## 🪟 Window Management Features

### Popup Configuration
- **Customizable dimensions** (width, height)
- **Position control** (left, top coordinates)
- **Multiple window support** with automatic positioning
- **Window state monitoring** (open/closed detection)

### Lifecycle Management
```javascript
// Parent opens child
const childWindow = window.open(url, '_blank', features);

// Child connects to parent
parentWindow = event.source;
parentOrigin = event.origin;

// Automatic cleanup on close
window.addEventListener('beforeunload', notifyParent);
```

## 🔒 Security Features

### Origin Validation
```javascript
// Parent validates child messages
if (event.origin !== window.location.origin) {
    log('SECURITY: Rejected unauthorized origin', 'security');
    return;
}

// Child validates parent messages
if (event.origin !== parentOrigin) {
    log('SECURITY: Rejected unauthorized origin', 'security');
    return;
}
```

### Message Structure Validation
```javascript
// Validate message format
if (!event.data || typeof event.data !== 'object') {
    log('SECURITY: Invalid message format', 'security');
    return;
}

const { type, payload, messageId } = event.data;
// Process only known message types
```

### Connection Security
- Parent reference stored securely in child
- Origin validation on every message
- Automatic cleanup of closed windows
- Graceful handling of connection loss

## 🐛 Debugging Features

### Comprehensive Logging
Both parent and child windows include:
- **Timestamped logs** with message types
- **Message counters** (sent/received)
- **Connection status** monitoring
- **Window state** tracking

### Visual Indicators
- **Connection status** (connected/disconnected)
- **Message statistics** (real-time counters)
- **Window information** (dimensions, position)
- **Parent information** (ID, URL, connection time)

### Console Integration
All messages logged to browser console:
```javascript
console.log(`Parent: Sent message to child ${childId}`);
console.log(`Child: Received ping from parent`);
```

## ⚠️ Browser Considerations

### Popup Blockers
- Modern browsers may block popups
- User interaction required for `window.open()`
- Provide fallback instructions for blocked popups

### Cross-Origin Restrictions
- Child windows must be same-origin for postMessage
- Different origins require explicit origin specification
- CORS policies don't apply to postMessage

### Window References
```javascript
// Parent keeps reference to child
const childWindow = window.open(url);

// Child gets reference to parent
const parentWindow = window.opener;

// Check if window is still open
if (!childWindow.closed) {
    // Window is still open
}
```

## 🔧 Advanced Features

### Work Simulation
Child windows can simulate long-running tasks:
```javascript
function simulateWork() {
    // Send work started notification
    sendToParent({ type: 'work_started', payload: { task: 'Processing' } });
    
    // Send progress updates
    let progress = 0;
    const interval = setInterval(() => {
        progress += 20;
        sendToParent({ type: 'work_progress', payload: { progress } });
        
        if (progress >= 100) {
            clearInterval(interval);
            sendToParent({ type: 'work_complete', payload: { result: 'Done' } });
        }
    }, 1000);
}
```

### Latency Measurement
```javascript
// Parent sends ping with timestamp
sendToChild({ type: 'ping', payload: { timestamp: Date.now() } });

// Child responds with pong
function sendPong(originalTimestamp) {
    sendToParent({ 
        type: 'pong', 
        payload: { 
            originalTimestamp,
            pongTimestamp: Date.now() 
        } 
    });
}

// Parent calculates latency
const latency = Date.now() - payload.originalTimestamp;
```

### Window State Tracking
```javascript
function sendWindowState() {
    const state = {
        position: { x: window.screenX, y: window.screenY },
        size: { width: window.innerWidth, height: window.innerHeight },
        focused: document.hasFocus(),
        visible: !document.hidden
    };
    sendToParent({ type: 'window_state', payload: state });
}
```

## 📚 Learning Points

1. **Use `window.opener`** in child to reference parent
2. **Store parent reference** on first message received
3. **Validate origins** for security
4. **Handle window lifecycle** (ready, closing events)
5. **Monitor window state** (open/closed detection)
6. **Implement graceful shutdown** with notifications
7. **Use unique message IDs** for tracking
8. **Provide comprehensive logging** for debugging

## 🚀 Extension Ideas

### Request-Response Pattern
```javascript
// Parent sends request with callback
const pendingRequests = new Map();

function sendRequest(type, payload, callback) {
    const requestId = generateUniqueId();
    pendingRequests.set(requestId, callback);
    sendToChild({ type, payload, requestId });
}

// Child sends response with request ID
function sendResponse(requestId, result) {
    sendToParent({ type: 'response', requestId, result });
}
```

### Child Window Registry
```javascript
class ChildWindowManager {
    constructor() {
        this.children = new Map();
    }
    
    addChild(id, window, metadata) {
        this.children.set(id, { window, metadata, lastSeen: Date.now() });
    }
    
    broadcast(message) {
        for (const [id, info] of this.children) {
            if (!info.window.closed) {
                info.window.postMessage(message, this.origin);
            }
        }
    }
    
    cleanup() {
        for (const [id, info] of this.children) {
            if (info.window.closed) {
                this.children.delete(id);
            }
        }
    }
}
```

### Event-Driven Architecture
```javascript
// Parent as event emitter
class ParentWindow extends EventTarget {
    sendToChild(childId, message) {
        // Send message
        this.dispatchEvent(new CustomEvent('messageSent', { detail: { childId, message } }));
    }
    
    onChildMessage(data) {
        this.dispatchEvent(new CustomEvent('childMessage', { detail: data }));
    }
}

// Usage
parent.addEventListener('childMessage', (event) => {
    console.log('Child message:', event.detail);
});
```

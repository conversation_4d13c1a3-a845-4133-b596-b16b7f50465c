#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting CDP Screencast POC...\n');

// Start signaling server
console.log('Starting signaling server...');
const server = spawn('npm', ['start'], {
  cwd: path.join(__dirname, '..', 'signaling-server'),
  stdio: 'inherit'
});

// Wait a moment then start client UI
setTimeout(() => {
  console.log('Starting client UI...');
  const client = spawn('npm', ['start'], {
    cwd: path.join(__dirname, '..', 'client-ui'),
    stdio: 'inherit'
  });

  // Handle cleanup
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down...');
    server.kill();
    client.kill();
    process.exit(0);
  });
}, 2000);

// Popup JavaScript for Video Stream Extension
class PopupManager {
  constructor() {
    this.currentTab = null;
    this.isCapturing = false;
    this.isStreaming = false;
    this.isConnectedToSignaling = false;
    this.captureStream = null;
    this.setupEventListeners();
    this.initialize();
  }

  setupEventListeners() {
    // Button event listeners
    document.getElementById('startCaptureBtn').addEventListener('click', () => {
      this.startCapture();
    });

    document.getElementById('stopCaptureBtn').addEventListener('click', () => {
      this.stopCapture();
    });

    document.getElementById('connectServerBtn').addEventListener('click', () => {
      this.connectToSignalingServer();
    });

    document.getElementById('startStreamBtn').addEventListener('click', () => {
      this.startStreaming();
    });

    document.getElementById('stopStreamBtn').addEventListener('click', () => {
      this.stopStreaming();
    });
  }

  async initialize() {
    try {
      // Get current tab information
      await this.loadCurrentTab();
      
      // Update UI
      this.updateUI();
      
      // Start periodic updates
      this.startPeriodicUpdates();
      
      this.log('Extension popup initialized');
    } catch (error) {
      this.log(`Initialization error: ${error.message}`, 'error');
    }
  }

  async loadCurrentTab() {
    try {
      const response = await this.sendMessage({ action: 'getActiveTab' });
      if (response.success) {
        this.currentTab = response.data;
        this.updateTabInfo();
      }
    } catch (error) {
      this.log(`Failed to load tab info: ${error.message}`, 'error');
    }
  }

  updateTabInfo() {
    if (this.currentTab) {
      document.getElementById('tabUrl').textContent = this.currentTab.url;
      document.getElementById('tabTitle').textContent = this.currentTab.title;
    }
  }

  async startCapture() {
    try {
      this.log('Starting video capture...');
      
      const response = await this.sendMessage({
        action: 'startCapture',
        tabId: this.currentTab.id
      });

      if (response.success) {
        this.isCapturing = true;
        this.captureStream = response.data.stream;
        
        // Display preview if stream is available
        if (this.captureStream) {
          this.displayVideoPreview(this.captureStream);
        }
        
        this.updateUI();
        this.log('Video capture started successfully', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to start capture: ${error.message}`, 'error');
    }
  }

  async stopCapture() {
    try {
      this.log('Stopping video capture...');
      
      const response = await this.sendMessage({
        action: 'stopCapture',
        tabId: this.currentTab.id
      });

      if (response.success) {
        this.isCapturing = false;
        this.captureStream = null;
        this.hideVideoPreview();
        this.updateUI();
        this.log('Video capture stopped', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to stop capture: ${error.message}`, 'error');
    }
  }

  async connectToSignalingServer() {
    try {
      const serverUrl = document.getElementById('signalingServer').value;
      if (!serverUrl) {
        throw new Error('Please enter a signaling server URL');
      }

      this.log(`Connecting to signaling server: ${serverUrl}`);
      
      const response = await this.sendMessage({
        action: 'connectSignaling',
        serverUrl: serverUrl
      });

      if (response.success) {
        this.isConnectedToSignaling = true;
        this.updateConnectionStatus('connected');
        this.updateUI();
        this.log('Connected to signaling server', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.isConnectedToSignaling = false;
      this.updateConnectionStatus('disconnected');
      this.log(`Failed to connect to signaling server: ${error.message}`, 'error');
    }
  }

  async startStreaming() {
    try {
      if (!this.isCapturing) {
        throw new Error('Start video capture first');
      }

      if (!this.isConnectedToSignaling) {
        throw new Error('Connect to signaling server first');
      }

      this.log('Starting video streaming...');
      
      const response = await this.sendMessage({
        action: 'startStreaming',
        tabId: this.currentTab.id
      });

      if (response.success) {
        this.isStreaming = true;
        this.updateUI();
        this.log('Video streaming started', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to start streaming: ${error.message}`, 'error');
    }
  }

  async stopStreaming() {
    try {
      this.log('Stopping video streaming...');
      
      const response = await this.sendMessage({
        action: 'stopStreaming'
      });

      if (response.success) {
        this.isStreaming = false;
        this.updateUI();
        this.log('Video streaming stopped', 'success');
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to stop streaming: ${error.message}`, 'error');
    }
  }

  displayVideoPreview(stream) {
    const video = document.getElementById('previewVideo');
    const overlay = document.getElementById('videoOverlay');
    
    if (stream && video) {
      video.srcObject = stream;
      overlay.classList.add('hidden');
    }
  }

  hideVideoPreview() {
    const video = document.getElementById('previewVideo');
    const overlay = document.getElementById('videoOverlay');
    
    if (video) {
      video.srcObject = null;
      overlay.classList.remove('hidden');
    }
  }

  updateConnectionStatus(status) {
    const statusIndicator = document.getElementById('connectionStatus');
    const statusText = statusIndicator.querySelector('.status-text');
    const statusDot = statusIndicator.querySelector('.status-dot');
    
    if (status === 'connected') {
      statusText.textContent = 'Connected';
      statusDot.classList.add('connected');
    } else {
      statusText.textContent = 'Disconnected';
      statusDot.classList.remove('connected');
    }
  }

  updateUI() {
    // Update button states
    document.getElementById('startCaptureBtn').disabled = this.isCapturing;
    document.getElementById('stopCaptureBtn').disabled = !this.isCapturing;
    document.getElementById('startStreamBtn').disabled = !this.isCapturing || !this.isConnectedToSignaling || this.isStreaming;
    document.getElementById('stopStreamBtn').disabled = !this.isStreaming;
  }

  async updateStreamInfo() {
    try {
      if (this.isCapturing && this.currentTab) {
        const response = await this.sendMessage({
          action: 'getCaptureStats',
          tabId: this.currentTab.id
        });

        if (response.success && response.data) {
          const stats = response.data;
          
          if (stats.video) {
            document.getElementById('resolution').textContent = 
              `${stats.video.width}x${stats.video.height}`;
            document.getElementById('frameRate').textContent = 
              `${Math.round(stats.video.frameRate || 0)} fps`;
          }
        }
      }

      if (this.isStreaming) {
        const response = await this.sendMessage({
          action: 'getStreamingStats'
        });

        if (response.success && response.data) {
          const stats = response.data;
          document.getElementById('connectedClients').textContent = stats.connectedClients;
        }
      }
    } catch (error) {
      // Silently handle errors in periodic updates
    }
  }

  startPeriodicUpdates() {
    // Update stream info every 2 seconds
    setInterval(() => {
      this.updateStreamInfo();
    }, 2000);
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  log(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('p');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
    
    // Keep only last 50 log entries
    while (logContainer.children.length > 50) {
      logContainer.removeChild(logContainer.firstChild);
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Stream Extension</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Video Stream</h1>
            <div class="status-indicator" id="connectionStatus">
                <span class="status-dot"></span>
                <span class="status-text">Disconnected</span>
            </div>
        </header>

        <main class="main-content">
            <!-- Tab Information -->
            <section class="tab-info">
                <h3>Current Tab</h3>
                <div class="tab-details" id="tabDetails">
                    <p class="tab-url" id="tabUrl">Loading...</p>
                    <p class="tab-title" id="tabTitle">Loading...</p>
                </div>
            </section>

            <!-- Video Preview -->
            <section class="video-preview">
                <h3>Video Preview</h3>
                <div class="video-container">
                    <video id="previewVideo" muted autoplay playsinline>
                        <p>Video preview not available</p>
                    </video>
                    <div class="video-overlay" id="videoOverlay">
                        <p>Click "Start Capture" to begin streaming</p>
                    </div>
                </div>
            </section>

            <!-- Controls -->
            <section class="controls">
                <div class="control-group">
                    <button id="startCaptureBtn" class="btn btn-primary">
                        Start Capture
                    </button>
                    <button id="stopCaptureBtn" class="btn btn-secondary" disabled>
                        Stop Capture
                    </button>
                </div>

                <div class="control-group">
                    <label for="signalingServer">Signaling Server:</label>
                    <input 
                        type="text" 
                        id="signalingServer" 
                        placeholder="ws://localhost:8080"
                        value="ws://localhost:8080"
                    >
                    <button id="connectServerBtn" class="btn btn-outline">
                        Connect
                    </button>
                </div>

                <div class="control-group">
                    <button id="startStreamBtn" class="btn btn-success" disabled>
                        Start Streaming
                    </button>
                    <button id="stopStreamBtn" class="btn btn-danger" disabled>
                        Stop Streaming
                    </button>
                </div>
            </section>

            <!-- Stream Information -->
            <section class="stream-info">
                <h3>Stream Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <label>Resolution:</label>
                        <span id="resolution">-</span>
                    </div>
                    <div class="info-item">
                        <label>Frame Rate:</label>
                        <span id="frameRate">-</span>
                    </div>
                    <div class="info-item">
                        <label>Bitrate:</label>
                        <span id="bitrate">-</span>
                    </div>
                    <div class="info-item">
                        <label>Connected Clients:</label>
                        <span id="connectedClients">0</span>
                    </div>
                </div>
            </section>

            <!-- Logs -->
            <section class="logs">
                <h3>Activity Log</h3>
                <div class="log-container" id="logContainer">
                    <p class="log-entry">Extension loaded</p>
                </div>
            </section>
        </main>
    </div>

    <script src="popup.js"></script>
</body>
</html>

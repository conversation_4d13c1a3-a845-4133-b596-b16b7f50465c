{"name": "cdp-screencast-client-ui", "version": "1.0.0", "description": "Web client UI for CDP screencast streaming", "main": "index.html", "scripts": {"start": "python3 -m http.server 8080 || python -m http.server 8080 || npx http-server -p 8080", "dev": "npx live-server --port=8080 --no-browser", "build": "echo 'No build step required for static files'", "test": "echo 'No tests configured'"}, "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2"}, "keywords": ["webrtc", "client-ui", "video-streaming", "cdp", "screencast"], "author": "CDP Screencast POC", "license": "MIT"}
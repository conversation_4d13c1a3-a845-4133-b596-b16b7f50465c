/**
 * CDP Screencast Module
 * 
 * Handles Chrome DevTools Protocol screencast functionality
 * with optimized frame handling and quality control.
 */

class CDPScreencast {
  constructor(cdpConnection) {
    this.cdp = cdpConnection;
    this.debug = true;
    
    // Screencast state
    this.isActive = false;
    this.currentSessionId = null;
    this.currentTargetId = null;
    this.attachedSessionId = null;
    
    // Frame handling
    this.frameCount = 0;
    this.lastFrameTime = 0;
    this.frameRate = 0;
    this.frameCallback = null;
    
    // Quality settings
    this.config = {
      format: 'jpeg',
      quality: 80,
      maxWidth: 1280,
      maxHeight: 720,
      everyNthFrame: 1
    };
    
    // Performance monitoring
    this.stats = {
      framesReceived: 0,
      framesProcessed: 0,
      averageFrameSize: 0,
      lastFrameTimestamp: 0
    };
  }

  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.log('Screencast config updated:', this.config);
  }

  setFrameCallback(callback) {
    this.frameCallback = callback;
  }

  async startScreencast(targetId, config = {}) {
    if (this.isActive) {
      this.log('Screencast already active, stopping current session...');
      await this.stopScreencast();
    }

    this.log(`Starting screencast for target: ${targetId}`);
    
    try {
      // Update config if provided
      if (Object.keys(config).length > 0) {
        this.updateConfig(config);
      }

      // Attach to target
      const attachResponse = await this.cdp.sendCommand('Target.attachToTarget', {
        targetId: targetId,
        flatten: true
      });

      this.attachedSessionId = attachResponse.result.sessionId;
      this.currentTargetId = targetId;
      
      this.log(`Attached to target with session: ${this.attachedSessionId}`);

      // Enable required domains
      await this.enableDomains();

      // Start screencast
      const screencastResponse = await this.cdp.sendCommand('Page.startScreencast', 
        this.config, 
        this.attachedSessionId
      );

      this.isActive = true;
      this.frameCount = 0;
      this.stats.framesReceived = 0;
      this.stats.framesProcessed = 0;
      
      this.log('Screencast started successfully');
      
      return {
        success: true,
        targetId: targetId,
        sessionId: this.attachedSessionId,
        config: this.config
      };

    } catch (error) {
      this.error('Failed to start screencast:', error);
      await this.cleanup();
      throw error;
    }
  }

  async stopScreencast() {
    if (!this.isActive) {
      this.log('Screencast not active');
      return;
    }

    this.log('Stopping screencast...');

    try {
      if (this.attachedSessionId) {
        await this.cdp.sendCommand('Page.stopScreencast', {}, this.attachedSessionId);
      }
      
      await this.cleanup();
      
      this.log('Screencast stopped successfully');
      
      return {
        success: true,
        stats: this.getStats()
      };

    } catch (error) {
      this.error('Error stopping screencast:', error);
      await this.cleanup();
      throw error;
    }
  }

  async enableDomains() {
    const domains = ['Page', 'Runtime', 'Target'];
    
    for (const domain of domains) {
      try {
        await this.cdp.sendCommand(`${domain}.enable`, {}, this.attachedSessionId);
        this.log(`${domain} domain enabled`);
      } catch (error) {
        this.error(`Failed to enable ${domain} domain:`, error);
      }
    }
  }

  handleScreencastFrame(params) {
    if (!this.isActive) return;

    this.stats.framesReceived++;
    this.frameCount++;
    
    const now = Date.now();
    
    // Calculate frame rate
    if (this.lastFrameTime > 0) {
      const timeDiff = now - this.lastFrameTime;
      this.frameRate = 1000 / timeDiff;
    }
    this.lastFrameTime = now;

    // Update stats
    this.stats.lastFrameTimestamp = now;
    if (params.data) {
      const frameSize = params.data.length;
      this.stats.averageFrameSize = 
        (this.stats.averageFrameSize * (this.stats.framesReceived - 1) + frameSize) / 
        this.stats.framesReceived;
    }

    // Process frame
    const frameData = {
      sessionId: params.sessionId,
      data: params.data,
      metadata: {
        frameNumber: this.frameCount,
        timestamp: now,
        frameRate: this.frameRate,
        targetId: this.currentTargetId
      }
    };

    // Call frame callback if set
    if (this.frameCallback) {
      try {
        this.frameCallback(frameData);
        this.stats.framesProcessed++;
      } catch (error) {
        this.error('Error in frame callback:', error);
      }
    }

    // Acknowledge frame
    this.acknowledgeFrame(params.sessionId);

    // Log performance periodically
    if (this.frameCount % 100 === 0) {
      this.logPerformance();
    }
  }

  async acknowledgeFrame(sessionId) {
    try {
      await this.cdp.sendCommand('Page.screencastFrameAck', {
        sessionId: sessionId
      }, this.attachedSessionId);
    } catch (error) {
      this.error('Failed to acknowledge frame:', error);
    }
  }

  async cleanup() {
    this.isActive = false;
    this.currentSessionId = null;
    this.currentTargetId = null;
    
    if (this.attachedSessionId) {
      try {
        await this.cdp.sendCommand('Target.detachFromTarget', {
          sessionId: this.attachedSessionId
        });
      } catch (error) {
        this.error('Error detaching from target:', error);
      }
      this.attachedSessionId = null;
    }
  }

  getStats() {
    return {
      ...this.stats,
      isActive: this.isActive,
      frameCount: this.frameCount,
      frameRate: this.frameRate,
      currentTarget: this.currentTargetId,
      config: this.config
    };
  }

  logPerformance() {
    const stats = this.getStats();
    this.log('Performance stats:', {
      frames: `${stats.framesProcessed}/${stats.framesReceived}`,
      frameRate: `${stats.frameRate.toFixed(1)} fps`,
      avgSize: `${(stats.averageFrameSize / 1024).toFixed(1)} KB`,
      target: stats.currentTarget
    });
  }

  // Quality control methods
  async adjustQuality(quality) {
    if (quality < 10 || quality > 100) {
      throw new Error('Quality must be between 10 and 100');
    }

    this.config.quality = quality;
    
    if (this.isActive) {
      // Restart screencast with new quality
      const targetId = this.currentTargetId;
      await this.stopScreencast();
      await this.startScreencast(targetId);
    }
  }

  async adjustResolution(width, height) {
    if (width < 320 || height < 240) {
      throw new Error('Minimum resolution is 320x240');
    }

    this.config.maxWidth = width;
    this.config.maxHeight = height;
    
    if (this.isActive) {
      // Restart screencast with new resolution
      const targetId = this.currentTargetId;
      await this.stopScreencast();
      await this.startScreencast(targetId);
    }
  }

  async adjustFrameSkip(everyNthFrame) {
    if (everyNthFrame < 1) {
      throw new Error('Frame skip must be at least 1');
    }

    this.config.everyNthFrame = everyNthFrame;
    
    if (this.isActive) {
      // Restart screencast with new frame skip
      const targetId = this.currentTargetId;
      await this.stopScreencast();
      await this.startScreencast(targetId);
    }
  }

  log(...args) {
    if (this.debug) {
      console.log('[CDPScreencast]', ...args);
    }
  }

  error(...args) {
    console.error('[CDPScreencast ERROR]', ...args);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CDPScreencast;
} else if (typeof window !== 'undefined') {
  window.CDPScreencast = CDPScreencast;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDP Screencast Viewer</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📺</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>
                    <span class="icon">📺</span>
                    CDP Screencast Viewer
                </h1>
                <div class="connection-status" id="connectionStatus">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <section class="control-panel">
                <div class="panel-header">
                    <h2>Browser Targets</h2>
                    <button id="refreshTargets" class="btn btn-secondary">
                        <span class="icon">🔄</span>
                        Refresh
                    </button>
                </div>
                
                <div class="targets-container">
                    <div id="targetsList" class="targets-list">
                        <div class="loading-message">
                            <span class="spinner"></span>
                            Discovering targets...
                        </div>
                    </div>
                </div>

                <!-- Quality Controls -->
                <div class="quality-controls">
                    <h3>Stream Quality</h3>
                    <div class="control-group">
                        <label for="qualitySlider">Quality:</label>
                        <input type="range" id="qualitySlider" min="10" max="100" value="80" step="10">
                        <span id="qualityValue">80%</span>
                    </div>
                    <div class="control-group">
                        <label for="resolutionSelect">Resolution:</label>
                        <select id="resolutionSelect">
                            <option value="1280x720">1280x720 (HD)</option>
                            <option value="1920x1080">1920x1080 (Full HD)</option>
                            <option value="854x480">854x480 (SD)</option>
                            <option value="640x360">640x360 (Low)</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- Video Display -->
            <section class="video-section">
                <div class="video-container">
                    <div id="videoDisplay" class="video-display">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">📺</div>
                            <h3>Select a target to start streaming</h3>
                            <p>Choose a browser tab from the targets list to begin video streaming</p>
                        </div>
                    </div>
                    
                    <!-- Video Controls -->
                    <div class="video-controls" id="videoControls" style="display: none;">
                        <div class="controls-left">
                            <button id="playPauseBtn" class="btn btn-primary">
                                <span class="icon">⏸️</span>
                                Pause
                            </button>
                            <button id="stopBtn" class="btn btn-secondary">
                                <span class="icon">⏹️</span>
                                Stop
                            </button>
                        </div>
                        
                        <div class="controls-center">
                            <span id="streamInfo" class="stream-info">
                                <span id="currentTarget">No target selected</span>
                                <span id="streamStats" class="stream-stats"></span>
                            </span>
                        </div>
                        
                        <div class="controls-right">
                            <button id="fullscreenBtn" class="btn btn-secondary">
                                <span class="icon">⛶</span>
                                Fullscreen
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Debug Panel (collapsible) -->
        <aside class="debug-panel" id="debugPanel">
            <div class="debug-header">
                <h3>Debug Information</h3>
                <button id="toggleDebug" class="btn btn-small">
                    <span class="icon">🔽</span>
                    Hide
                </button>
            </div>
            <div class="debug-content" id="debugContent">
                <div class="debug-section">
                    <h4>Connection Status</h4>
                    <div id="connectionDebug" class="debug-info">
                        <div>WebSocket: <span id="wsStatus">Disconnected</span></div>
                        <div>WebRTC: <span id="webrtcStatus">Not connected</span></div>
                        <div>Manager: <span id="managerStatus">Not ready</span></div>
                    </div>
                </div>
                
                <div class="debug-section">
                    <h4>Stream Statistics</h4>
                    <div id="streamDebug" class="debug-info">
                        <div>Frames Received: <span id="framesReceived">0</span></div>
                        <div>Frame Rate: <span id="frameRate">0 fps</span></div>
                        <div>Data Rate: <span id="dataRate">0 KB/s</span></div>
                        <div>Latency: <span id="latency">0 ms</span></div>
                    </div>
                </div>
                
                <div class="debug-section">
                    <h4>Message Log</h4>
                    <div id="messageLog" class="message-log"></div>
                    <button id="clearLog" class="btn btn-small">Clear Log</button>
                </div>
            </div>
        </aside>

        <!-- Error Modal -->
        <div id="errorModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Error</h3>
                    <button id="closeError" class="btn btn-small">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage">An error occurred</p>
                </div>
                <div class="modal-footer">
                    <button id="retryBtn" class="btn btn-primary">Retry</button>
                    <button id="dismissError" class="btn btn-secondary">Dismiss</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="webrtc-handler.js"></script>
    <script src="app.js"></script>
</body>
</html>

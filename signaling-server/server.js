// WebSocket Signaling Server for Video Stream Extension
const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const path = require('path');

class SignalingServer {
  constructor(port = 8080) {
    this.port = port;
    this.clients = new Map();
    this.extensionConnection = null;
    this.setupExpressServer();
    this.setupWebSocketServer();
  }

  setupExpressServer() {
    this.app = express();
    this.app.use(cors());
    this.app.use(express.static(path.join(__dirname, 'public')));
    
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        connectedClients: this.clients.size,
        extensionConnected: !!this.extensionConnection,
        timestamp: new Date().toISOString()
      });
    });

    // API endpoint to get connected clients
    this.app.get('/api/clients', (req, res) => {
      const clientList = Array.from(this.clients.entries()).map(([id, client]) => ({
        id,
        connected: client.ws.readyState === WebSocket.OPEN,
        userAgent: client.userAgent,
        connectedAt: client.connectedAt
      }));
      
      res.json({
        clients: clientList,
        total: clientList.length
      });
    });

    this.httpServer = this.app.listen(this.port, () => {
      console.log(`Signaling server running on port ${this.port}`);
      console.log(`Web client available at: http://localhost:${this.port}`);
      console.log(`WebSocket endpoint: ws://localhost:${this.port}`);
    });
  }

  setupWebSocketServer() {
    this.wss = new WebSocket.Server({ server: this.httpServer });
    
    this.wss.on('connection', (ws, req) => {
      this.handleNewConnection(ws, req);
    });

    console.log('WebSocket server initialized');
  }

  handleNewConnection(ws, req) {
    const clientId = this.generateClientId();
    const userAgent = req.headers['user-agent'] || 'Unknown';
    const isExtension = userAgent.includes('Chrome') && req.headers.origin?.includes('extension');
    
    console.log(`New connection: ${clientId} (${isExtension ? 'Extension' : 'Web Client'})`);

    if (isExtension) {
      this.extensionConnection = {
        ws,
        clientId,
        connectedAt: new Date().toISOString()
      };
      console.log('Extension connected');
    } else {
      this.clients.set(clientId, {
        ws,
        clientId,
        userAgent,
        connectedAt: new Date().toISOString()
      });
      
      // Notify extension about new client
      this.notifyExtension('client-connected', { clientId });
    }

    // Set up message handling
    ws.on('message', (data) => {
      this.handleMessage(ws, data, clientId, isExtension);
    });

    // Handle disconnection
    ws.on('close', () => {
      this.handleDisconnection(clientId, isExtension);
    });

    // Handle errors
    ws.on('error', (error) => {
      console.error(`WebSocket error for ${clientId}:`, error);
    });

    // Send welcome message
    this.sendToClient(ws, {
      type: 'connected',
      clientId,
      message: `Connected as ${isExtension ? 'extension' : 'client'}`
    });
  }

  handleMessage(ws, data, clientId, isExtension) {
    try {
      const message = JSON.parse(data.toString());
      console.log(`Message from ${clientId}:`, message.type);

      if (isExtension) {
        // Message from extension - forward to specific client or broadcast
        this.handleExtensionMessage(message);
      } else {
        // Message from web client - forward to extension
        this.handleClientMessage(message, clientId);
      }
    } catch (error) {
      console.error(`Error parsing message from ${clientId}:`, error);
      this.sendToClient(ws, {
        type: 'error',
        message: 'Invalid message format'
      });
    }
  }

  handleExtensionMessage(message) {
    const { type, clientId, data } = message;

    switch (type) {
      case 'offer':
      case 'answer':
      case 'ice-candidate':
        // Forward WebRTC signaling to specific client
        if (clientId && this.clients.has(clientId)) {
          const client = this.clients.get(clientId);
          this.sendToClient(client.ws, {
            type,
            data,
            from: 'extension'
          });
        }
        break;

      case 'broadcast':
        // Broadcast message to all clients
        this.broadcastToClients({
          type: 'broadcast',
          data,
          from: 'extension'
        });
        break;

      default:
        console.warn('Unknown message type from extension:', type);
    }
  }

  handleClientMessage(message, clientId) {
    const { type, data } = message;

    // Forward client messages to extension
    if (this.extensionConnection) {
      this.sendToClient(this.extensionConnection.ws, {
        type,
        clientId,
        data,
        from: 'client'
      });
    } else {
      // No extension connected
      const client = this.clients.get(clientId);
      if (client) {
        this.sendToClient(client.ws, {
          type: 'error',
          message: 'Extension not connected'
        });
      }
    }
  }

  handleDisconnection(clientId, isExtension) {
    if (isExtension) {
      console.log('Extension disconnected');
      this.extensionConnection = null;
      
      // Notify all clients that extension disconnected
      this.broadcastToClients({
        type: 'extension-disconnected',
        message: 'Extension disconnected'
      });
    } else {
      console.log(`Client disconnected: ${clientId}`);
      this.clients.delete(clientId);
      
      // Notify extension about client disconnection
      this.notifyExtension('client-disconnected', { clientId });
    }
  }

  notifyExtension(type, data) {
    if (this.extensionConnection && 
        this.extensionConnection.ws.readyState === WebSocket.OPEN) {
      this.sendToClient(this.extensionConnection.ws, {
        type,
        ...data
      });
    }
  }

  broadcastToClients(message) {
    this.clients.forEach((client) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        this.sendToClient(client.ws, message);
      }
    });
  }

  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  generateClientId() {
    return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  getStats() {
    return {
      connectedClients: this.clients.size,
      extensionConnected: !!this.extensionConnection,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };
  }

  shutdown() {
    console.log('Shutting down signaling server...');
    
    // Close all client connections
    this.clients.forEach((client) => {
      client.ws.close();
    });
    
    // Close extension connection
    if (this.extensionConnection) {
      this.extensionConnection.ws.close();
    }
    
    // Close WebSocket server
    this.wss.close();
    
    // Close HTTP server
    this.httpServer.close();
    
    console.log('Signaling server shut down');
  }
}

// Create and start the server
const server = new SignalingServer(process.env.PORT || 8080);

// Graceful shutdown
process.on('SIGINT', () => {
  server.shutdown();
  process.exit(0);
});

process.on('SIGTERM', () => {
  server.shutdown();
  process.exit(0);
});

module.exports = SignalingServer;

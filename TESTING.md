# Testing Guide for Video Stream Extension

This guide provides comprehensive testing procedures for the Chrome Extension Video Streaming system.

## Pre-Testing Setup

### Environment Requirements
- Chrome Browser (latest version)
- Node.js (v14+)
- Local network access
- Microphone/camera permissions (for testing)

### Initial Setup
```bash
# 1. Install signaling server dependencies
cd signaling-server
npm install

# 2. Start signaling server
npm start

# 3. Load extension in Chrome
# - Open chrome://extensions/
# - Enable Developer mode
# - Load unpacked extension
```

## Test Categories

### 1. Extension Loading Tests

#### Test 1.1: Extension Installation
**Objective**: Verify extension loads without errors

**Steps**:
1. Open `chrome://extensions/`
2. Enable Developer mode
3. Click "Load unpacked"
4. Select project directory
5. Verify extension appears in list

**Expected Result**: Extension loads successfully with no errors

**Validation**:
- Extension icon appears in toolbar
- No error messages in extension list
- Extension popup opens when clicked

#### Test 1.2: Permissions Verification
**Objective**: Confirm all required permissions are granted

**Steps**:
1. Click extension icon
2. Check for permission prompts
3. Verify manifest permissions

**Expected Result**: All permissions properly declared and accessible

### 2. Video Capture Tests

#### Test 2.1: Basic Tab Capture
**Objective**: Test basic video capture functionality

**Steps**:
1. Open a test webpage (e.g., YouTube video)
2. Click extension icon
3. Click "Start Capture"
4. Select tab in capture dialog
5. Grant permissions

**Expected Result**: 
- Capture dialog appears
- Video preview shows in extension popup
- No error messages

**Validation**:
- Preview video displays content
- Capture button becomes disabled
- Stop button becomes enabled

#### Test 2.2: Multiple Tab Capture
**Objective**: Test capturing different tabs

**Steps**:
1. Open multiple tabs with different content
2. Switch between tabs
3. Start capture on each tab
4. Verify correct content is captured

**Expected Result**: Each tab's content is captured correctly

#### Test 2.3: Capture Error Handling
**Objective**: Test error scenarios

**Steps**:
1. Cancel capture dialog
2. Try capturing restricted pages (chrome://)
3. Test with no tabs open

**Expected Result**: Appropriate error messages displayed

### 3. CDP Integration Tests

#### Test 3.1: CDP Attachment
**Objective**: Verify CDP can attach to tabs

**Steps**:
1. Open extension popup
2. Navigate to a test page
3. Check browser console for CDP messages
4. Verify no attachment errors

**Expected Result**: CDP attaches successfully without errors

#### Test 3.2: Enhanced Page Access
**Objective**: Test CDP enhanced capabilities

**Steps**:
1. Open a page with video elements
2. Use CDP to get page information
3. Verify enhanced data is available

**Expected Result**: CDP provides additional page data beyond standard APIs

#### Test 3.3: CDP Error Handling
**Objective**: Test CDP failure scenarios

**Steps**:
1. Try CDP on restricted pages
2. Test with multiple CDP attachments
3. Verify cleanup on tab close

**Expected Result**: Graceful error handling and cleanup

### 4. Signaling Server Tests

#### Test 4.1: Server Startup
**Objective**: Verify signaling server starts correctly

**Steps**:
1. Run `npm start` in signaling-server directory
2. Check console output
3. Verify server responds on port 8080

**Expected Result**: 
- Server starts without errors
- WebSocket endpoint available
- HTTP server serves web client

#### Test 4.2: WebSocket Connections
**Objective**: Test WebSocket connectivity

**Steps**:
1. Open web client (http://localhost:8080)
2. Click "Connect" button
3. Check connection status
4. Verify server logs show connection

**Expected Result**: 
- Client connects successfully
- Server logs show new connection
- Connection status updates in UI

#### Test 4.3: Message Routing
**Objective**: Test signaling message routing

**Steps**:
1. Connect extension to signaling server
2. Connect web client to signaling server
3. Start streaming from extension
4. Verify messages are routed correctly

**Expected Result**: Messages flow between extension and client

### 5. WebRTC Streaming Tests

#### Test 5.1: Peer Connection Establishment
**Objective**: Test WebRTC connection setup

**Steps**:
1. Start video capture in extension
2. Connect both extension and client to signaling server
3. Start streaming from extension
4. Monitor WebRTC connection states

**Expected Result**: 
- Peer connection establishes successfully
- ICE candidates are exchanged
- Connection state becomes "connected"

#### Test 5.2: Video Streaming
**Objective**: Test actual video transmission

**Steps**:
1. Establish WebRTC connection
2. Start streaming from extension
3. Verify video appears in web client
4. Check video quality and smoothness

**Expected Result**: 
- Video stream appears in client
- Reasonable quality and frame rate
- Audio included if available

#### Test 5.3: Multiple Clients
**Objective**: Test streaming to multiple clients

**Steps**:
1. Open multiple web client tabs
2. Connect all clients to signaling server
3. Start streaming from extension
4. Verify all clients receive stream

**Expected Result**: All connected clients receive video stream

### 6. Performance Tests

#### Test 6.1: CPU Usage
**Objective**: Monitor system resource usage

**Steps**:
1. Start video capture and streaming
2. Monitor CPU usage in Task Manager
3. Test with different video qualities
4. Check for memory leaks

**Expected Result**: Reasonable CPU usage, no memory leaks

#### Test 6.2: Network Bandwidth
**Objective**: Test network usage

**Steps**:
1. Monitor network usage during streaming
2. Test with different network conditions
3. Verify adaptive bitrate works

**Expected Result**: Appropriate bandwidth usage, adapts to network conditions

#### Test 6.3: Latency Testing
**Objective**: Measure streaming latency

**Steps**:
1. Use timer/clock on captured tab
2. Compare with received video
3. Measure end-to-end latency

**Expected Result**: Low latency (< 500ms for local network)

### 7. Error Handling Tests

#### Test 7.1: Network Disconnection
**Objective**: Test behavior when network fails

**Steps**:
1. Establish streaming connection
2. Disconnect network
3. Reconnect network
4. Verify recovery behavior

**Expected Result**: Graceful handling of disconnection and reconnection

#### Test 7.2: Server Restart
**Objective**: Test server failure scenarios

**Steps**:
1. Establish connections
2. Restart signaling server
3. Verify client behavior
4. Test reconnection

**Expected Result**: Clients detect disconnection and can reconnect

#### Test 7.3: Extension Reload
**Objective**: Test extension reload scenarios

**Steps**:
1. Start streaming
2. Reload extension
3. Verify cleanup
4. Test restart capability

**Expected Result**: Clean shutdown and restart capability

## Automated Testing

### Unit Tests
```bash
# Run unit tests (if implemented)
cd signaling-server
npm test
```

### Integration Tests
Create automated tests for:
- WebSocket connection establishment
- WebRTC signaling flow
- Video capture initialization
- Error scenarios

### Performance Benchmarks
- Measure capture startup time
- Monitor streaming latency
- Track resource usage over time

## Test Data and Scenarios

### Test Content
Use these types of content for testing:
- Static web pages
- Video content (YouTube, etc.)
- Interactive web applications
- Canvas-based content
- WebGL applications

### Network Conditions
Test under various conditions:
- Local network (low latency)
- Simulated slow network
- High packet loss scenarios
- Bandwidth-limited connections

## Debugging and Troubleshooting

### Debug Tools
1. **Chrome DevTools**: For extension debugging
2. **Extension Console**: Background script debugging
3. **Network Tab**: Monitor WebSocket/WebRTC traffic
4. **Performance Tab**: Profile resource usage

### Common Issues and Solutions

#### "User cancelled capture"
- Ensure tab selection in capture dialog
- Check permissions are granted

#### "WebRTC connection failed"
- Verify STUN servers are accessible
- Check firewall settings
- Monitor ICE candidate exchange

#### "Signaling server not responding"
- Confirm server is running
- Check WebSocket URL
- Verify network connectivity

### Log Analysis
Monitor these logs for issues:
- Extension background script console
- Signaling server console output
- Web client browser console
- Chrome extension error logs

## Test Reporting

### Test Results Template
```
Test: [Test Name]
Date: [Date]
Environment: [Browser version, OS]
Result: [Pass/Fail]
Issues: [Any issues encountered]
Performance: [Relevant metrics]
Notes: [Additional observations]
```

### Performance Metrics
Track these metrics:
- Capture startup time
- WebRTC connection establishment time
- Video streaming latency
- CPU usage during streaming
- Memory usage over time
- Network bandwidth utilization

## Continuous Testing

### Regression Testing
- Test after each code change
- Verify existing functionality still works
- Check for performance regressions

### Browser Compatibility
- Test on different Chrome versions
- Verify on different operating systems
- Check with various hardware configurations

### Load Testing
- Test with multiple simultaneous streams
- Verify server handles multiple clients
- Check resource limits and scaling

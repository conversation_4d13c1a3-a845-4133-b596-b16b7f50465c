class WebRTCSignaling {
  constructor() {
    this.debug = process.env.DEBUG === 'true';
    this.activeSessions = new Map(); // sessionId -> { clientId, managerId, state }
    this.stunServers = [
      'stun:stun.cloudflare.com:3478',
      'stun:stun.l.google.com:19302'
    ];
    
    // TURN server configuration (for production)
    this.turnServers = process.env.TURN_SERVERS ? 
      JSON.parse(process.env.TURN_SERVERS) : [];
  }

  createSession(clientId, managerId, targetId) {
    const sessionId = `${clientId}-${managerId}-${Date.now()}`;
    
    const session = {
      id: sessionId,
      clientId: clientId,
      managerId: managerId,
      targetId: targetId,
      state: 'initializing',
      createdAt: new Date(),
      lastActivity: new Date(),
      iceServers: this.getIceServers()
    };

    this.activeSessions.set(sessionId, session);
    this.log(`Created WebRTC session: ${sessionId}`);
    
    return session;
  }

  getSession(sessionId) {
    return this.activeSessions.get(sessionId);
  }

  updateSessionState(sessionId, state) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.state = state;
      session.lastActivity = new Date();
      this.log(`Session ${sessionId} state: ${state}`);
    }
  }

  handleOffer(sessionId, offer, fromClientId) {
    const session = this.getSession(sessionId);
    if (!session) {
      this.error(`Session not found: ${sessionId}`);
      return null;
    }

    this.log(`Handling offer for session ${sessionId}`);
    session.offer = offer;
    session.lastActivity = new Date();
    
    return {
      type: 'webrtc-offer',
      sessionId: sessionId,
      offer: offer,
      iceServers: session.iceServers,
      fromClientId: fromClientId
    };
  }

  handleAnswer(sessionId, answer, fromClientId) {
    const session = this.getSession(sessionId);
    if (!session) {
      this.error(`Session not found: ${sessionId}`);
      return null;
    }

    this.log(`Handling answer for session ${sessionId}`);
    session.answer = answer;
    session.lastActivity = new Date();
    this.updateSessionState(sessionId, 'connecting');
    
    return {
      type: 'webrtc-answer',
      sessionId: sessionId,
      answer: answer,
      fromClientId: fromClientId
    };
  }

  handleIceCandidate(sessionId, candidate, fromClientId) {
    const session = this.getSession(sessionId);
    if (!session) {
      this.error(`Session not found: ${sessionId}`);
      return null;
    }

    this.log(`Handling ICE candidate for session ${sessionId}`);
    session.lastActivity = new Date();
    
    return {
      type: 'webrtc-ice-candidate',
      sessionId: sessionId,
      candidate: candidate,
      fromClientId: fromClientId
    };
  }

  handleConnectionStateChange(sessionId, state) {
    const session = this.getSession(sessionId);
    if (!session) return;

    this.log(`WebRTC connection state for ${sessionId}: ${state}`);
    
    switch (state) {
      case 'connected':
        this.updateSessionState(sessionId, 'connected');
        break;
      case 'disconnected':
        this.updateSessionState(sessionId, 'disconnected');
        break;
      case 'failed':
        this.updateSessionState(sessionId, 'failed');
        this.cleanupSession(sessionId);
        break;
      case 'closed':
        this.cleanupSession(sessionId);
        break;
    }
  }

  cleanupSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.log(`Cleaning up session: ${sessionId}`);
      this.activeSessions.delete(sessionId);
    }
  }

  cleanupExpiredSessions() {
    const now = new Date();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    for (const [sessionId, session] of this.activeSessions) {
      if (now - session.lastActivity > maxAge) {
        this.log(`Cleaning up expired session: ${sessionId}`);
        this.activeSessions.delete(sessionId);
      }
    }
  }

  getIceServers() {
    const servers = [
      ...this.stunServers.map(url => ({ urls: url })),
      ...this.turnServers
    ];

    return servers;
  }

  getSessionStats() {
    const stats = {
      total: this.activeSessions.size,
      byState: {}
    };

    for (const session of this.activeSessions.values()) {
      stats.byState[session.state] = (stats.byState[session.state] || 0) + 1;
    }

    return stats;
  }

  getAllSessions() {
    return Array.from(this.activeSessions.values()).map(session => ({
      id: session.id,
      clientId: session.clientId,
      managerId: session.managerId,
      targetId: session.targetId,
      state: session.state,
      createdAt: session.createdAt,
      lastActivity: session.lastActivity
    }));
  }

  // Utility method to validate WebRTC messages
  validateMessage(message) {
    const requiredFields = {
      'webrtc-offer': ['sessionId', 'offer'],
      'webrtc-answer': ['sessionId', 'answer'],
      'webrtc-ice-candidate': ['sessionId', 'candidate']
    };

    const required = requiredFields[message.type];
    if (!required) return false;

    return required.every(field => message.hasOwnProperty(field));
  }

  // Generate WebRTC configuration for clients
  getClientConfig() {
    return {
      iceServers: this.getIceServers(),
      iceCandidatePoolSize: 10,
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require'
    };
  }

  log(...args) {
    if (this.debug) {
      console.log(`[WebRTCSignaling]`, ...args);
    }
  }

  error(...args) {
    console.error(`[WebRTCSignaling ERROR]`, ...args);
  }
}

module.exports = WebRTCSignaling;

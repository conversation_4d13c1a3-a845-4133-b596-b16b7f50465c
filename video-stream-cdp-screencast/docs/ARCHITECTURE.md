# CDP Screencast System Architecture

## System Overview

The CDP Screencast POC is a three-component system that enables real-time browser content streaming using Chrome DevTools Protocol (CDP) screencast functionality combined with WebRTC for efficient peer-to-peer video transmission.

## Component Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Signaling Server                           │
│                   (Node.js + Express)                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐  │
│  │  Browser        │ │  WebSocket      │ │  WebRTC         │  │
│  │  Manager        │ │  Server         │ │  Signaling      │  │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │  Chrome Browser │ │  CDP Manager    │ │  Client UI      │
    │  (Target Tabs)  │ │  Tab (Injected) │ │  (Web App)      │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
            │                   │                   │
            ▼                   ▼                   ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │  • google.com   │ │  CDP WebSocket  │ │  WebRTC Client  │
    │  • github.com   │ │  Connection     │ │  Video Display  │
    │  • stackoverflow│ │  Screencast API │ │  Target Controls│
    └─────────────────┘ └─────────────────┘ └─────────────────┘
```

## Data Flow

### 1. Initialization Flow
```
1. Signaling Server starts → Launch Chrome with CDP
2. Chrome opens → Create manager tab + target tabs
3. Manager tab → Inject CDP controller script
4. CDP Controller → Connect to ws://localhost:9222/json
5. CDP Controller → Discover targets using Target.getTargets()
6. CDP Controller → Report available targets to signaling server
```

### 2. Client Connection Flow
```
1. Client UI → Connect to signaling server via WebSocket
2. Signaling Server → Send available targets list to client
3. Client → Display targets in UI with metadata
4. User → Select target from dropdown/grid
5. Client → Send target selection to signaling server
6. Signaling Server → Forward selection to CDP controller
```

### 3. Video Streaming Flow
```
1. CDP Controller → Page.startScreencast() on selected target
2. Target Tab → Generate screencast frames
3. CDP Controller → Receive frames via CDP WebSocket
4. CDP Controller → Initiate WebRTC connection with client
5. WebRTC → Direct peer-to-peer video stream
6. Client UI → Display real-time video stream
```

## Component Details

### Signaling Server
**Location**: `signaling-server/`

**Responsibilities**:
- Launch and manage Chrome browser instances
- Handle WebSocket connections from clients and CDP controller
- Coordinate WebRTC signaling (offer/answer/ICE candidates)
- Manage target tab lifecycle
- Provide REST API for browser management

**Key Files**:
- `server.js`: Main Express server with WebSocket support
- `browser-manager.js`: Chrome browser lifecycle management
- `webrtc-signaling.js`: WebRTC signaling coordination

**APIs**:
- `GET /api/targets`: List available browser targets
- `POST /api/browser/launch`: Launch new browser instance
- `POST /api/browser/close`: Close browser instance
- `WebSocket /ws`: Real-time signaling and control

### CDP Controller Script
**Location**: `cdp-controller/`

**Responsibilities**:
- Connect to Chrome DevTools Protocol WebSocket
- Discover and enumerate browser targets
- Implement screencast functionality
- Establish WebRTC peer connections
- Handle target switching and cleanup

**Key Files**:
- `manager.js`: Main CDP controller logic
- `screencast.js`: CDP screencast implementation
- `webrtc-client.js`: WebRTC peer connection management

**CDP APIs Used**:
- `Target.getTargets()`: Discover available targets
- `Target.attachToTarget()`: Attach to specific target
- `Page.startScreencast()`: Begin screencast capture
- `Page.stopScreencast()`: End screencast capture

### Client UI
**Location**: `client-ui/`

**Responsibilities**:
- Display available browser targets
- Handle WebRTC client connections
- Show real-time video streams
- Provide target switching controls
- Display connection status and debug info

**Key Files**:
- `index.html`: Main UI structure
- `app.js`: Application logic and UI management
- `webrtc-handler.js`: WebRTC client implementation
- `style.css`: Responsive styling

## Communication Protocols

### WebSocket Messages

**Target Discovery**:
```javascript
{
  type: 'targets',
  targets: [
    {
      id: 'target-1',
      title: 'Google',
      url: 'https://google.com',
      favicon: 'https://google.com/favicon.ico',
      type: 'page'
    }
  ]
}
```

**Target Selection**:
```javascript
{
  type: 'selectTarget',
  targetId: 'target-1',
  clientId: 'client-123'
}
```

**WebRTC Signaling**:
```javascript
{
  type: 'offer' | 'answer' | 'ice-candidate',
  sdp: '...',
  candidate: { ... },
  targetId: 'target-1',
  clientId: 'client-123'
}
```

### CDP Protocol Messages

**Target Discovery**:
```javascript
{
  id: 1,
  method: 'Target.getTargets',
  params: {}
}
```

**Screencast Start**:
```javascript
{
  id: 2,
  method: 'Page.startScreencast',
  params: {
    format: 'jpeg',
    quality: 80,
    maxWidth: 1280,
    maxHeight: 720,
    everyNthFrame: 1
  }
}
```

## Security Considerations

### Development Mode
- Chrome launched with `--disable-web-security`
- Unencrypted WebSocket connections (ws://)
- No authentication or authorization
- CORS disabled for development

### Production Recommendations
- Use WSS (WebSocket Secure) for encrypted connections
- Implement proper authentication and authorization
- Enable CORS with specific allowed origins
- Add rate limiting and input validation
- Use HTTPS for all web content
- Implement proper session management

## Performance Characteristics

### Video Quality Settings
- **Format**: JPEG for screencast frames
- **Quality**: 80% (configurable)
- **Resolution**: Up to 1280x720 (configurable)
- **Frame Rate**: Adaptive based on content changes
- **Latency**: ~100-300ms end-to-end

### Resource Usage
- **CPU**: Moderate (screencast encoding + WebRTC)
- **Memory**: ~100-300MB per active stream
- **Network**: ~1-3 Mbps per stream (depends on content)
- **Browser**: Additional ~50-100MB per target tab

## Error Handling

### Connection Failures
- Automatic WebSocket reconnection with exponential backoff
- WebRTC connection state monitoring and recovery
- CDP connection failure detection and retry logic

### Resource Cleanup
- Proper screencast stopping on client disconnect
- Target tab cleanup on browser close
- WebRTC peer connection cleanup
- Memory leak prevention

## Scalability Considerations

### Current Limitations
- Single browser instance per server
- Limited concurrent client connections
- No horizontal scaling support
- In-memory state management

### Scaling Strategies
1. **Multi-browser Support**: Launch multiple Chrome instances
2. **Load Balancing**: Distribute clients across server instances
3. **State Management**: Use Redis for shared state
4. **CDN Integration**: Stream distribution via CDN edges

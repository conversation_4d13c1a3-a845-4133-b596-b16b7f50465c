/**
 * CDP Screencast Client Application
 *
 * Main application logic for the client UI that connects to the signaling server
 * and displays screencast streams from browser targets.
 */

class CDPScreencastApp {
  constructor() {
    this.debug = true;
    this.signalingServerUrl = "ws://localhost:3000";

    // Components
    this.webrtcHandler = new WebRTCHandler();
    this.signalingSocket = null;
    this.clientId = null;

    // State
    this.availableTargets = new Map();
    this.selectedTarget = null;
    this.isStreaming = false;
    this.managerReady = false;

    // UI elements
    this.elements = {};

    // Statistics
    this.stats = {
      framesReceived: 0,
      frameRate: 0,
      dataRate: 0,
      latency: 0,
      connectionState: "disconnected",
    };

    this.init();
  }

  async init() {
    this.log("Initializing CDP Screencast Client...");

    try {
      this.initializeUI();
      this.setupEventHandlers();
      this.setupWebRTCCallbacks();

      await this.connectToSignalingServer();

      this.log("Client initialized successfully");
    } catch (error) {
      this.error("Failed to initialize client:", error);
      this.showError("Failed to initialize application", error.message);
    }
  }

  initializeUI() {
    // Cache DOM elements
    this.elements = {
      // Status elements
      connectionStatus: document.getElementById("connectionStatus"),
      statusIndicator: document.getElementById("statusIndicator"),
      statusText: document.getElementById("statusText"),

      // Target elements
      targetsList: document.getElementById("targetsList"),
      refreshTargets: document.getElementById("refreshTargets"),

      // Video elements
      videoDisplay: document.getElementById("videoDisplay"),
      videoControls: document.getElementById("videoControls"),
      currentTarget: document.getElementById("currentTarget"),
      streamStats: document.getElementById("streamStats"),

      // Control elements
      playPauseBtn: document.getElementById("playPauseBtn"),
      stopBtn: document.getElementById("stopBtn"),
      fullscreenBtn: document.getElementById("fullscreenBtn"),

      // Quality controls
      qualitySlider: document.getElementById("qualitySlider"),
      qualityValue: document.getElementById("qualityValue"),
      resolutionSelect: document.getElementById("resolutionSelect"),

      // Debug elements
      debugPanel: document.getElementById("debugPanel"),
      toggleDebug: document.getElementById("toggleDebug"),
      debugContent: document.getElementById("debugContent"),
      wsStatus: document.getElementById("wsStatus"),
      webrtcStatus: document.getElementById("webrtcStatus"),
      managerStatus: document.getElementById("managerStatus"),
      framesReceived: document.getElementById("framesReceived"),
      frameRate: document.getElementById("frameRate"),
      dataRate: document.getElementById("dataRate"),
      latency: document.getElementById("latency"),
      messageLog: document.getElementById("messageLog"),
      clearLog: document.getElementById("clearLog"),

      // Error modal
      errorModal: document.getElementById("errorModal"),
      errorMessage: document.getElementById("errorMessage"),
      closeError: document.getElementById("closeError"),
      dismissError: document.getElementById("dismissError"),
      retryBtn: document.getElementById("retryBtn"),
    };

    this.updateConnectionStatus("connecting", "Connecting to server...");
  }

  setupEventHandlers() {
    // Target refresh
    this.elements.refreshTargets.addEventListener("click", () => {
      this.requestTargets();
    });

    // Video controls
    this.elements.playPauseBtn.addEventListener("click", () => {
      this.togglePlayPause();
    });

    this.elements.stopBtn.addEventListener("click", () => {
      this.stopStreaming();
    });

    this.elements.fullscreenBtn.addEventListener("click", () => {
      this.toggleFullscreen();
    });

    // Quality controls
    this.elements.qualitySlider.addEventListener("input", (e) => {
      const quality = e.target.value;
      this.elements.qualityValue.textContent = quality + "%";
      this.webrtcHandler.requestQualityChange(parseInt(quality));
    });

    this.elements.resolutionSelect.addEventListener("change", (e) => {
      const [width, height] = e.target.value.split("x").map(Number);
      this.webrtcHandler.requestResolutionChange(width, height);
    });

    // Debug panel
    this.elements.toggleDebug.addEventListener("click", () => {
      this.toggleDebugPanel();
    });

    this.elements.clearLog.addEventListener("click", () => {
      this.elements.messageLog.innerHTML = "";
    });

    // Error modal
    this.elements.closeError.addEventListener("click", () => {
      this.hideError();
    });

    this.elements.dismissError.addEventListener("click", () => {
      this.hideError();
    });

    this.elements.retryBtn.addEventListener("click", () => {
      this.hideError();
      this.connectToSignalingServer();
    });
  }

  setupWebRTCCallbacks() {
    this.webrtcHandler.setFrameCallback((frameData) => {
      this.handleFrame(frameData);
    });

    this.webrtcHandler.setStatsCallback((stats) => {
      this.updateStats(stats);
    });
  }

  async connectToSignalingServer() {
    this.log("Connecting to signaling server...");

    try {
      this.signalingSocket = new WebSocket(this.signalingServerUrl);
      this.webrtcHandler.setSignalingSocket(this.signalingSocket);

      this.signalingSocket.onopen = () => {
        this.log("Connected to signaling server");
        this.updateConnectionStatus("connected", "Connected");
        this.updateDebugStatus("wsStatus", "Connected");
      };

      this.signalingSocket.onmessage = (event) => {
        this.handleSignalingMessage(JSON.parse(event.data));
      };

      this.signalingSocket.onclose = () => {
        this.log("Signaling server connection closed");
        this.updateConnectionStatus("error", "Disconnected");
        this.updateDebugStatus("wsStatus", "Disconnected");

        // Attempt reconnection
        setTimeout(() => {
          this.connectToSignalingServer();
        }, 5000);
      };

      this.signalingSocket.onerror = (error) => {
        this.error("Signaling server error:", error);
        this.updateConnectionStatus("error", "Connection failed");
        this.updateDebugStatus("wsStatus", "Error");
      };
    } catch (error) {
      this.error("Failed to connect to signaling server:", error);
      this.showError(
        "Connection Failed",
        "Could not connect to signaling server"
      );
    }
  }

  handleSignalingMessage(message) {
    this.logMessage("Received", message);

    switch (message.type) {
      case "welcome":
        this.handleWelcome(message);
        break;
      case "targets":
        this.handleTargets(message);
        break;
      case "managerReady":
        this.handleManagerReady(message);
        break;
      case "managerDisconnected":
        this.handleManagerDisconnected();
        break;
      case "webrtc-offer":
        this.webrtcHandler.handleOffer(message.offer);
        break;
      case "webrtc-answer":
        this.webrtcHandler.handleAnswer(message.answer);
        break;
      case "webrtc-ice-candidate":
        this.webrtcHandler.handleIceCandidate(message.candidate);
        break;
      default:
        this.log("Unknown message type:", message.type);
    }
  }

  handleWelcome(message) {
    this.clientId = message.clientId;
    this.webrtcHandler.clientId = this.clientId;
    this.log("Client ID assigned:", this.clientId);

    // Identify as UI client
    this.sendSignalingMessage({
      type: "identify",
      clientType: "ui",
      userAgent: navigator.userAgent,
    });

    // Request available targets
    this.requestTargets();
  }

  handleTargets(message) {
    this.log("Targets received:", message.targets.length);
    this.availableTargets.clear();

    message.targets.forEach((target) => {
      this.availableTargets.set(target.id, target);
    });

    this.renderTargets();
  }

  handleManagerReady(message) {
    this.log("Manager ready with capabilities:", message.capabilities);
    this.managerReady = true;
    this.updateDebugStatus("managerStatus", "Ready");
    this.updateConnectionStatus("connected", "Ready to stream");
  }

  handleManagerDisconnected() {
    this.log("Manager disconnected");
    this.managerReady = false;
    this.updateDebugStatus("managerStatus", "Disconnected");
    this.updateConnectionStatus("error", "Manager disconnected");

    if (this.isStreaming) {
      this.stopStreaming();
    }
  }

  renderTargets() {
    const container = this.elements.targetsList;
    container.innerHTML = "";

    if (this.availableTargets.size === 0) {
      container.innerHTML = `
        <div class="loading-message">
          <span class="icon">📭</span>
          No targets available
        </div>
      `;
      return;
    }

    this.availableTargets.forEach((target) => {
      const targetElement = this.createTargetElement(target);
      container.appendChild(targetElement);
    });
  }

  createTargetElement(target) {
    const element = document.createElement("div");
    element.className = "target-item";
    element.dataset.targetId = target.id;

    element.innerHTML = `
      <div class="target-favicon">
        <img src="${target.favicon}" alt="" onerror="this.style.display='none'">
        <span class="icon" style="display: none;">🌐</span>
      </div>
      <div class="target-info">
        <div class="target-title">${target.title}</div>
        <div class="target-url">${target.url}</div>
      </div>
    `;

    element.addEventListener("click", () => {
      this.selectTarget(target);
    });

    return element;
  }

  selectTarget(target) {
    if (!this.managerReady) {
      this.showError(
        "Manager Not Ready",
        "The CDP manager is not ready. Please wait."
      );
      return;
    }

    this.log("Selecting target:", target.title);

    // Update UI
    document.querySelectorAll(".target-item").forEach((el) => {
      el.classList.remove("selected");
    });

    const targetElement = document.querySelector(
      `[data-target-id="${target.id}"]`
    );
    if (targetElement) {
      targetElement.classList.add("selected");
    }

    this.selectedTarget = target;
    this.elements.currentTarget.textContent = target.title;

    // Send selection to manager
    this.sendSignalingMessage({
      type: "selectTarget",
      targetId: target.id,
    });

    // Show video controls
    this.elements.videoControls.style.display = "flex";
    this.isStreaming = true;

    // Clear placeholder
    this.elements.videoDisplay.innerHTML = `
      <div class="placeholder-content">
        <div class="placeholder-icon">⏳</div>
        <h3>Connecting to ${target.title}...</h3>
        <p>Establishing WebRTC connection and starting screencast</p>
      </div>
    `;
  }

  handleFrame(frameData) {
    if (frameData.type === "videoTrack") {
      this.displayVideoTrack(frameData.stream);
    } else if (frameData.type === "screencastFrame") {
      this.displayScreencastFrame(frameData);
    }
  }

  displayVideoTrack(stream) {
    this.log("Displaying video track");

    let videoElement = this.elements.videoDisplay.querySelector("video");
    if (!videoElement) {
      videoElement = document.createElement("video");
      videoElement.className = "video-element";
      videoElement.autoplay = true;
      videoElement.muted = true;
      this.elements.videoDisplay.innerHTML = "";
      this.elements.videoDisplay.appendChild(videoElement);
    }

    videoElement.srcObject = stream;
  }

  displayScreencastFrame(frameData) {
    // For base64 encoded JPEG frames from CDP screencast
    if (frameData.data) {
      let imgElement = this.elements.videoDisplay.querySelector("img");
      if (!imgElement) {
        imgElement = document.createElement("img");
        imgElement.className = "video-element";
        imgElement.style.width = "100%";
        imgElement.style.height = "auto";
        this.elements.videoDisplay.innerHTML = "";
        this.elements.videoDisplay.appendChild(imgElement);
      }

      imgElement.src = `data:image/jpeg;base64,${frameData.data}`;
    }
  }

  requestTargets() {
    this.sendSignalingMessage({
      type: "getTargets",
    });
  }

  togglePlayPause() {
    // Implementation for play/pause functionality
    this.log("Toggle play/pause");
  }

  stopStreaming() {
    this.log("Stopping stream");

    this.isStreaming = false;
    this.selectedTarget = null;

    // Clear video display
    this.elements.videoDisplay.innerHTML = `
      <div class="placeholder-content">
        <div class="placeholder-icon">📺</div>
        <h3>Select a target to start streaming</h3>
        <p>Choose a browser tab from the targets list to begin video streaming</p>
      </div>
    `;

    // Hide video controls
    this.elements.videoControls.style.display = "none";

    // Clear target selection
    document.querySelectorAll(".target-item").forEach((el) => {
      el.classList.remove("selected");
    });

    // Cleanup WebRTC
    this.webrtcHandler.cleanup();
  }

  toggleFullscreen() {
    const videoContainer = this.elements.videoDisplay;

    if (!document.fullscreenElement) {
      videoContainer.requestFullscreen().catch((err) => {
        this.error("Error attempting to enable fullscreen:", err);
      });
    } else {
      document.exitFullscreen();
    }
  }

  updateStats(stats) {
    Object.assign(this.stats, stats);

    // Update debug panel
    if (stats.framesReceived !== undefined) {
      this.updateDebugStatus("framesReceived", stats.framesReceived);
    }
    if (stats.frameRate !== undefined) {
      this.updateDebugStatus("frameRate", stats.frameRate + " fps");
    }
    if (stats.dataRate !== undefined) {
      this.updateDebugStatus("dataRate", stats.dataRate + " KB/s");
    }
    if (stats.latency !== undefined) {
      this.updateDebugStatus("latency", stats.latency + " ms");
    }
    if (stats.connectionState !== undefined) {
      this.updateDebugStatus("webrtcStatus", stats.connectionState);
    }

    // Update stream stats in video controls
    if (this.isStreaming) {
      this.elements.streamStats.textContent = `${stats.frameRate || 0} fps • ${
        stats.dataRate || 0
      } KB/s`;
    }
  }

  updateConnectionStatus(status, text) {
    this.elements.statusIndicator.className = `status-indicator ${status}`;
    this.elements.statusText.textContent = text;
  }

  updateDebugStatus(elementId, value) {
    const element = this.elements[elementId];
    if (element) {
      element.textContent = value;
    }
  }

  toggleDebugPanel() {
    const panel = this.elements.debugPanel;
    const button = this.elements.toggleDebug;

    if (panel.classList.contains("collapsed")) {
      panel.classList.remove("collapsed");
      button.innerHTML = '<span class="icon">🔽</span> Hide';
    } else {
      panel.classList.add("collapsed");
      button.innerHTML = '<span class="icon">🔼</span> Show';
    }
  }

  showError(title, message) {
    this.elements.errorMessage.textContent = message;
    this.elements.errorModal.style.display = "flex";
  }

  hideError() {
    this.elements.errorModal.style.display = "none";
  }

  sendSignalingMessage(message) {
    if (
      this.signalingSocket &&
      this.signalingSocket.readyState === WebSocket.OPEN
    ) {
      this.signalingSocket.send(JSON.stringify(message));
      this.logMessage("Sent", message);
    }
  }

  logMessage(direction, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement("div");
    logEntry.innerHTML = `
      <span style="color: #64748b;">[${timestamp}]</span>
      <span style="color: ${
        direction === "Sent" ? "#10b981" : "#2563eb"
      };">${direction}:</span>
      ${message.type}
    `;

    this.elements.messageLog.appendChild(logEntry);
    this.elements.messageLog.scrollTop = this.elements.messageLog.scrollHeight;

    // Keep only last 100 messages
    while (this.elements.messageLog.children.length > 100) {
      this.elements.messageLog.removeChild(this.elements.messageLog.firstChild);
    }
  }

  log(...args) {
    if (this.debug) {
      console.log("[CDPScreencastApp]", ...args);
    }
  }

  error(...args) {
    console.error("[CDPScreencastApp ERROR]", ...args);
  }
}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.cdpApp = new CDPScreencastApp();
});

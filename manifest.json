{"manifest_version": 3, "name": "Video Stream Extension", "version": "1.0.0", "description": "Stream video content from browser tabs to external web clients using WebRTC and CDP", "permissions": ["activeTab", "tabs", "desktopCapture", "debugger", "storage"], "host_permissions": ["http://localhost:*/*", "https://localhost:*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_title": "Video Stream Extension", "default_icon": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}}, "icons": {"16": "icon.png", "32": "icon.png", "48": "icon.png", "128": "icon.png"}, "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["<all_urls>"]}]}
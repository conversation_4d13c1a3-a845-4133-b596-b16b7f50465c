/* Popup styles for Tab Screen Share Extension */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 380px;
    min-height: 500px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4757;
    animation: pulse 2s infinite;
}

.status-dot.capturing {
    background: #2ed573;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    padding: 16px;
}

section {
    margin-bottom: 20px;
}

section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

/* Tab Information */
.tab-details {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #4CAF50;
}

.tab-url {
    font-size: 12px;
    color: #666;
    word-break: break-all;
    margin-bottom: 4px;
}

.tab-title {
    font-weight: 500;
    color: #333;
}

/* Video Preview */
.video-container {
    position: relative;
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    aspect-ratio: 16/9;
}

#previewVideo {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    text-align: center;
    padding: 20px;
}

.video-overlay.hidden {
    display: none;
}

/* Controls */
.control-group {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

/* Buttons */
.btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #45a049;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

/* Capture Information */
.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.info-item {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
}

.info-item label {
    font-weight: 500;
    color: #666;
    display: block;
    margin-bottom: 2px;
}

.info-item span {
    color: #333;
    font-weight: 600;
}

/* Activity Log */
.log-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    max-height: 100px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

.log-entry {
    margin-bottom: 4px;
    color: #666;
}

.log-entry:last-child {
    margin-bottom: 0;
}

.log-entry.error {
    color: #dc3545;
}

.log-entry.success {
    color: #28a745;
}

.log-entry.info {
    color: #17a2b8;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .container {
        width: 100%;
        min-height: 100vh;
        border-radius: 0;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting PostMessage Demo Servers...\n');

// Start Server 1 (Origin 1)
const server1 = spawn('node', ['server1.js'], {
  cwd: __dirname,
  stdio: 'inherit'
});

// Start Server 2 (Origin 2)
const server2 = spawn('node', ['server2.js'], {
  cwd: __dirname,
  stdio: 'inherit'
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');
  server1.kill();
  server2.kill();
  process.exit(0);
});

server1.on('error', (err) => {
  console.error('❌ Server 1 error:', err);
});

server2.on('error', (err) => {
  console.error('❌ Server 2 error:', err);
});

console.log('✅ Servers starting...');
console.log('📍 Server 1 (Origin 1): http://localhost:3001');
console.log('📍 Server 2 (Origin 2): http://localhost:3002');
console.log('\n💡 Press Ctrl+C to stop all servers');

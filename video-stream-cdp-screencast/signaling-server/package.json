{"name": "cdp-screencast-signaling-server", "version": "1.0.0", "description": "WebSocket + WebRTC orchestration server for CDP screencast streaming", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test.js"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "puppeteer": "^21.5.2", "chrome-launcher": "^1.1.0", "cors": "^2.8.5", "uuid": "^9.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["webrtc", "websocket", "cdp", "chrome-devtools-protocol", "signaling"], "author": "CDP Screencast POC", "license": "MIT"}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Complex Multi-Tab - Tab 1 (Communicator)</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        min-height: 100vh;
      }
      .container {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 20px;
      }
      .header {
        text-align: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .role-badge {
        display: inline-block;
        background: #4ecdc4;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        margin: 10px 0;
      }
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }
      .status-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      .status-card h4 {
        margin: 0 0 10px 0;
        color: #4ecdc4;
      }
      .controls {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 10px;
        margin: 15px 0;
        align-items: center;
      }
      .control-row {
        display: flex;
        gap: 10px;
        margin: 10px 0;
        flex-wrap: wrap;
      }
      input[type="text"] {
        padding: 12px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        flex: 1;
        min-width: 200px;
      }
      button {
        padding: 12px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
      }
      .btn-primary {
        background: #4ecdc4;
        color: white;
      }
      .btn-secondary {
        background: #ffe66d;
        color: #333;
      }
      .btn-danger {
        background: #ff6b6b;
        color: white;
      }
      .btn-success {
        background: #51cf66;
        color: white;
      }
      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
      button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }
      .log {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 15px;
        height: 300px;
        overflow-y: auto;
        font-family: "Courier New", monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
      .network-map {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }
      .node-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
      }
      .node-item {
        background: rgba(255, 255, 255, 0.2);
        padding: 12px;
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .node-info {
        flex: 1;
      }
      .node-status {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
      }
      .status-online {
        background: #51cf66;
        color: white;
      }
      .status-offline {
        background: #ff6b6b;
        color: white;
      }
      .instructions {
        background: rgba(255, 255, 255, 0.05);
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 4px solid #4ecdc4;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🌐 Complex Multi-Tab Communication</h1>
        <div class="role-badge">Tab 1 - Communicator</div>
        <p>This tab communicates with child windows opened by other tabs</p>
        <p><strong>Tab ID:</strong> <span id="tabId"></span></p>
      </div>

      <div class="instructions">
        <h4>📋 Instructions</h4>
        <ol>
          <li>Open <strong>Tab 2</strong> in another browser tab</li>
          <li>Use Tab 2 to open child windows</li>
          <li>Use this tab to communicate with those child windows</li>
          <li>
            Observe the complex message routing between Tab1 ↔ Child (opened by
            Tab2)
          </li>
        </ol>
      </div>

      <div class="status-grid">
        <div class="status-card">
          <h4>🔗 Network Status</h4>
          <div id="networkStatus">Initializing...</div>
        </div>
        <div class="status-card">
          <h4>📡 Communication</h4>
          <div id="communicationStatus">Ready</div>
        </div>
        <div class="status-card">
          <h4>📊 Messages</h4>
          <div id="messageStats">Sent: 0, Received: 0</div>
        </div>
      </div>

      <div class="network-map">
        <h4>🗺️ Network Map</h4>
        <div id="networkNodes" class="node-list">
          <div style="color: #ccc; font-style: italic">
            No other nodes detected
          </div>
        </div>
      </div>

      <div class="container">
        <h3>📤 Send Messages</h3>
        <div class="controls">
          <input
            type="text"
            id="messageInput"
            placeholder="Enter message to send to child windows..."
            value="Hello from Tab 1!"
          />
          <button class="btn-primary" onclick="sendToChildWindows()">
            Send to Children
          </button>
        </div>

        <div class="control-row">
          <button class="btn-secondary" onclick="requestNetworkInfo()">
            Request Network Info
          </button>
          <button class="btn-secondary" onclick="sendDataPacket()">
            Send Data Packet
          </button>
          <button class="btn-success" onclick="pingNetwork()">
            Ping Network
          </button>
        </div>

        <div class="control-row">
          <button class="btn-secondary" onclick="requestChildList()">
            Request Child List
          </button>
          <button class="btn-danger" onclick="sendEmergencySignal()">
            Emergency Signal
          </button>
        </div>
      </div>

      <div class="container">
        <h3>📋 Communication Log</h3>
        <div id="messageLog" class="log"></div>
        <button
          onclick="clearLog()"
          style="
            margin-top: 10px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 4px;
          "
        >
          Clear Log
        </button>
      </div>
    </div>

    <script>
      // Tab identification
      const TAB_ID =
        "tab1_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
      const TAB_NAME = "Tab 1 (Communicator)";
      const TAB_ROLE = "communicator";

      // Communication channels
      let broadcastChannel;
      let networkNodes = new Map();
      let messageCounter = 0;
      let sentMessages = 0;
      let receivedMessages = 0;

      // DOM elements
      const messageInput = document.getElementById("messageInput");
      const messageLog = document.getElementById("messageLog");
      const networkNodesDisplay = document.getElementById("networkNodes");

      // Initialize
      document.getElementById("tabId").textContent = TAB_ID;

      function log(message, type = "info") {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
        messageLog.textContent += logEntry;
        messageLog.scrollTop = messageLog.scrollHeight;
        console.log(`Tab1: ${message}`);
      }

      function updateStats() {
        document.getElementById(
          "messageStats"
        ).textContent = `Sent: ${sentMessages}, Received: ${receivedMessages}`;
      }

      function updateNetworkStatus() {
        const totalNodes = networkNodes.size;
        const onlineNodes = Array.from(networkNodes.values()).filter(
          (node) => !node.offline
        ).length;
        document.getElementById(
          "networkStatus"
        ).textContent = `${onlineNodes}/${totalNodes} nodes online`;
      }

      function updateNetworkDisplay() {
        if (networkNodes.size === 0) {
          networkNodesDisplay.innerHTML =
            '<div style="color: #ccc; font-style: italic;">No other nodes detected</div>';
        } else {
          networkNodesDisplay.innerHTML = Array.from(networkNodes.entries())
            .map(
              ([id, info]) => `
                        <div class="node-item">
                            <div class="node-info">
                                <strong>${info.name}</strong> (${info.role})<br>
                                <small>ID: ${id}</small>
                            </div>
                            <div class="node-status ${
                              info.offline ? "status-offline" : "status-online"
                            }">
                                ${info.offline ? "Offline" : "Online"}
                            </div>
                        </div>
                    `
            )
            .join("");
        }
        updateNetworkStatus();
      }

      // Initialize BroadcastChannel for inter-tab communication
      function initBroadcastChannel() {
        try {
          broadcastChannel = new BroadcastChannel("multi-tab-complex");
          broadcastChannel.addEventListener("message", handleBroadcastMessage);
          document.getElementById("communicationStatus").textContent =
            "BroadcastChannel ready";
          log("🔗 BroadcastChannel initialized", "success");
        } catch (error) {
          document.getElementById("communicationStatus").textContent =
            "BroadcastChannel failed";
          log(`❌ BroadcastChannel error: ${error.message}`, "error");
        }
      }

      // Handle messages from other tabs/windows
      function handleBroadcastMessage(event) {
        const data = event.data;
        log(`📡 Received via broadcast: ${data.type}`, "receive");
        processMessage(data, "broadcast");
      }

      // Listen for direct postMessage from child windows
      window.addEventListener("message", function (event) {
        // Validate origin
        if (event.origin !== window.location.origin) {
          log(
            `❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`,
            "security"
          );
          return;
        }

        // Validate message structure
        if (!event.data || typeof event.data !== "object") {
          log(`❌ SECURITY: Invalid message format received`, "security");
          return;
        }

        log(`📥 Received direct message: ${event.data.type}`, "receive");
        processMessage(event.data, "direct");
      });

      // Process incoming messages
      function processMessage(data, method) {
        const { type, payload, senderId, senderName, senderRole, timestamp } =
          data;
        receivedMessages++;
        updateStats();

        // Update network node information
        if (senderId && senderId !== TAB_ID) {
          updateNetworkNode(senderId, senderName, senderRole, method);
        }

        switch (type) {
          case "node_announcement":
            log(`📢 Node announcement from ${senderName}`, "network");
            log(`  Role: ${payload.role}`, "network");
            log(
              `  Capabilities: ${payload.capabilities.join(", ")}`,
              "network"
            );
            break;

          case "child_window_opened":
            log(`🪟 Child window opened by ${senderName}`, "network");
            log(`  Child ID: ${payload.childId}`, "network");
            log(`  Child URL: ${payload.childUrl}`, "network");
            // Store child window reference for direct communication
            networkNodes.set(payload.childId, {
              name: payload.childName || "Child Window",
              role: "child_window",
              parentId: senderId,
              parentName: senderName,
              offline: false,
              lastSeen: Date.now(),
            });
            updateNetworkDisplay();
            break;

          case "child_window_closed":
            log(`🪟 Child window closed: ${payload.childId}`, "network");
            networkNodes.delete(payload.childId);
            updateNetworkDisplay();
            break;

          case "child_message":
            log(
              `💬 Message from child ${payload.childId}: "${payload.message}"`,
              "message"
            );
            break;

          case "network_info_response":
            log(`📊 Network info from ${senderName}:`, "info");
            log(`  Connected children: ${payload.childCount}`, "info");
            log(`  Active connections: ${payload.activeConnections}`, "info");
            break;

          case "ping_response":
            const latency = Date.now() - payload.originalTimestamp;
            log(`🏓 Ping response from ${senderName} (${latency}ms)`, "ping");
            break;

          case "heartbeat":
            log(`💓 Heartbeat from ${senderName}`, "heartbeat");
            break;

          case "emergency_response":
            log(
              `🚨 Emergency response from ${senderName}: ${payload.status}`,
              "emergency"
            );
            break;

          case "node_disconnect":
            log(`👋 ${senderName} disconnected`, "network");
            if (networkNodes.has(senderId)) {
              networkNodes.get(senderId).offline = true;
              updateNetworkDisplay();
            }
            break;

          default:
            log(`⚠️ Unknown message type: ${type}`, "warning");
        }
      }

      function updateNetworkNode(nodeId, nodeName, nodeRole, method) {
        networkNodes.set(nodeId, {
          name: nodeName || "Unknown Node",
          role: nodeRole || "unknown",
          method: method,
          offline: false,
          lastSeen: Date.now(),
        });
        updateNetworkDisplay();
      }

      // Send message via broadcast channel
      function sendBroadcast(data) {
        if (broadcastChannel) {
          try {
            broadcastChannel.postMessage(data);
            sentMessages++;
            updateStats();
            return true;
          } catch (error) {
            log(`❌ Failed to send broadcast: ${error.message}`, "error");
            return false;
          }
        } else {
          log("❌ BroadcastChannel not available", "error");
          return false;
        }
      }

      function sendToChildWindows() {
        const message = messageInput.value.trim();
        if (!message) {
          log("❌ Cannot send empty message", "error");
          return;
        }

        const messageData = {
          type: "message_to_children",
          payload: {
            message: message,
            targetRole: "child_window",
          },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(messageData)) {
          log(`📤 Sent message to child windows: "${message}"`, "send");
          messageInput.value = "";
        }
      }

      function requestNetworkInfo() {
        const requestData = {
          type: "network_info_request",
          payload: {},
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(requestData)) {
          log("📋 Requested network information", "send");
        }
      }

      function sendDataPacket() {
        const dataPacket = {
          type: "data_packet",
          payload: {
            data: {
              timestamp: new Date().toISOString(),
              sequence: messageCounter + 1,
              payload: "Complex data from Tab 1",
              metadata: {
                source: TAB_NAME,
                priority: "normal",
                encryption: false,
              },
            },
            targetRole: "child_window",
          },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(dataPacket)) {
          log("📦 Sent data packet to network", "send");
        }
      }

      function pingNetwork() {
        const pingData = {
          type: "ping_request",
          payload: {
            originalTimestamp: Date.now(),
            targetRole: "all",
          },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(pingData)) {
          log("🏓 Pinged entire network", "send");
        }
      }

      function requestChildList() {
        const requestData = {
          type: "child_list_request",
          payload: {},
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(requestData)) {
          log("📋 Requested child window list", "send");
        }
      }

      function sendEmergencySignal() {
        if (confirm("Send emergency signal to all network nodes?")) {
          const emergencyData = {
            type: "emergency_signal",
            payload: {
              message: "Emergency signal from Tab 1",
              level: "high",
              action: "acknowledge",
            },
            senderId: TAB_ID,
            senderName: TAB_NAME,
            senderRole: TAB_ROLE,
            messageId: ++messageCounter,
            timestamp: Date.now(),
          };

          if (sendBroadcast(emergencyData)) {
            log("🚨 Sent emergency signal to network", "send");
          }
        }
      }

      function clearLog() {
        messageLog.textContent = "";
        log("📋 Log cleared", "info");
      }

      // Handle Enter key in message input
      messageInput.addEventListener("keypress", function (event) {
        if (event.key === "Enter") {
          sendToChildWindows();
        }
      });

      // Send node announcement when loaded
      function announceNode() {
        const announcementData = {
          type: "node_announcement",
          payload: {
            role: TAB_ROLE,
            capabilities: [
              "messaging",
              "data_processing",
              "network_monitoring",
            ],
            version: "1.0.0",
          },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        if (sendBroadcast(announcementData)) {
          log("📢 Announced node to network", "send");
        }
      }

      // Send disconnect signal when tab is closing
      window.addEventListener("beforeunload", function () {
        const disconnectData = {
          type: "node_disconnect",
          payload: { reason: "Tab closing" },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          timestamp: Date.now(),
        };

        if (broadcastChannel) {
          try {
            broadcastChannel.postMessage(disconnectData);
          } catch (error) {
            // Ignore errors during shutdown
          }
        }
      });

      // Periodically clean up offline nodes
      setInterval(() => {
        const now = Date.now();
        const timeout = 30000; // 30 seconds

        for (const [nodeId, info] of networkNodes.entries()) {
          if (now - info.lastSeen > timeout) {
            info.offline = true;
          }
        }
        updateNetworkDisplay();
      }, 5000);

      // Send periodic heartbeats
      setInterval(() => {
        const heartbeatData = {
          type: "heartbeat",
          payload: { timestamp: Date.now() },
          senderId: TAB_ID,
          senderName: TAB_NAME,
          senderRole: TAB_ROLE,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        sendBroadcast(heartbeatData);
      }, 10000); // Every 10 seconds

      // Initialize everything
      initBroadcastChannel();

      // Announce this node after a short delay
      setTimeout(announceNode, 1000);

      log("🚀 Tab 1 (Communicator) initialized", "success");
      log("Waiting for Tab 2 to open child windows...", "info");
    </script>
  </body>
</html>

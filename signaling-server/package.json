{"name": "video-stream-signaling-server", "version": "1.0.0", "description": "WebSocket signaling server for Chrome extension video streaming", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["webrtc", "signaling", "websocket", "video-streaming"], "author": "", "license": "MIT", "dependencies": {"ws": "^8.14.2", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}
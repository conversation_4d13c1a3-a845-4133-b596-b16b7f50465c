<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Parent-Child Window Communication - Parent</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        min-height: 100vh;
      }
      .container {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 20px;
      }
      .header {
        text-align: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }
      .status-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      .status-card h4 {
        margin: 0 0 10px 0;
        color: #a8e6cf;
      }
      .controls {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 10px;
        margin: 15px 0;
        align-items: center;
      }
      .control-row {
        display: flex;
        gap: 10px;
        margin: 10px 0;
        flex-wrap: wrap;
      }
      input[type="text"],
      input[type="url"] {
        padding: 12px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        flex: 1;
        min-width: 200px;
      }
      button {
        padding: 12px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
      }
      .btn-primary {
        background: #4ecdc4;
        color: white;
      }
      .btn-secondary {
        background: #ffe66d;
        color: #333;
      }
      .btn-danger {
        background: #ff6b6b;
        color: white;
      }
      .btn-success {
        background: #51cf66;
        color: white;
      }
      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
      button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }
      .log {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 15px;
        height: 300px;
        overflow-y: auto;
        font-family: "Courier New", monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
      .child-windows {
        background: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }
      .window-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
      }
      .window-item {
        background: rgba(255, 255, 255, 0.2);
        padding: 12px;
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .window-info {
        flex: 1;
      }
      .window-controls {
        display: flex;
        gap: 5px;
      }
      .window-controls button {
        padding: 6px 12px;
        font-size: 12px;
      }
      .popup-config {
        background: rgba(255, 255, 255, 0.05);
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }
      .config-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 10px 0;
      }
      .config-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .config-item label {
        font-size: 14px;
        min-width: 80px;
      }
      .config-item input {
        flex: 1;
        padding: 6px;
        border-radius: 4px;
        border: none;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🪟 Parent-Child Window Communication - Parent</h1>
        <p>
          This parent window can open and communicate with child popup windows
        </p>
        <p><strong>Parent ID:</strong> <span id="parentId"></span></p>
      </div>

      <div class="status-grid">
        <div class="status-card">
          <h4>🪟 Child Windows</h4>
          <div id="childWindowCount">0 windows open</div>
        </div>
        <div class="status-card">
          <h4>📡 Communication</h4>
          <div id="communicationStatus">Ready to communicate</div>
        </div>
        <div class="status-card">
          <h4>📊 Messages</h4>
          <div id="messageStats">Sent: 0, Received: 0</div>
        </div>
      </div>

      <div class="child-windows">
        <h4>🔗 Open Child Windows</h4>
        <div id="childWindowsList" class="window-list">
          <div style="color: #ccc; font-style: italic">
            No child windows open
          </div>
        </div>
      </div>

      <div class="container popup-config">
        <h3>⚙️ Popup Configuration</h3>
        <div class="config-grid">
          <div class="config-item">
            <label>Width:</label>
            <input
              type="number"
              id="popupWidth"
              value="600"
              min="300"
              max="1200"
            />
          </div>
          <div class="config-item">
            <label>Height:</label>
            <input
              type="number"
              id="popupHeight"
              value="500"
              min="200"
              max="800"
            />
          </div>
          <div class="config-item">
            <label>Left:</label>
            <input type="number" id="popupLeft" value="100" />
          </div>
          <div class="config-item">
            <label>Top:</label>
            <input type="number" id="popupTop" value="100" />
          </div>
        </div>
        <div class="control-row">
          <input
            type="url"
            id="childUrl"
            placeholder="Child window URL..."
            value="/parent-child-window/child.html"
          />
          <button class="btn-primary" onclick="openChildWindow()">
            Open Child Window
          </button>
          <button class="btn-secondary" onclick="openMultipleWindows()">
            Open 3 Windows
          </button>
          <button class="btn-danger" onclick="closeAllWindows()">
            Close All
          </button>
        </div>
      </div>

      <div class="container">
        <h3>📤 Send Messages</h3>
        <div class="controls">
          <input
            type="text"
            id="messageInput"
            placeholder="Enter message to send to all child windows..."
            value="Hello from parent!"
          />
          <button class="btn-primary" onclick="sendMessageToAll()">
            Send to All
          </button>
        </div>

        <div class="control-row">
          <button class="btn-secondary" onclick="requestChildInfo()">
            Request Child Info
          </button>
          <button class="btn-secondary" onclick="sendDataUpdate()">
            Send Data Update
          </button>
          <button class="btn-success" onclick="pingAllChildren()">
            Ping All Children
          </button>
          <button class="btn-danger" onclick="sendShutdownSignal()">
            Send Shutdown
          </button>
        </div>
      </div>

      <div class="container">
        <h3>📋 Communication Log</h3>
        <div id="messageLog" class="log"></div>
        <button
          onclick="clearLog()"
          style="
            margin-top: 10px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 4px;
          "
        >
          Clear Log
        </button>
      </div>
    </div>

    <script>
      // Parent window identification
      const PARENT_ID =
        "parent_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);

      // State management
      let childWindows = new Map();
      let messageCounter = 0;
      let sentMessages = 0;
      let receivedMessages = 0;

      // DOM elements
      const messageInput = document.getElementById("messageInput");
      const messageLog = document.getElementById("messageLog");
      const childWindowsList = document.getElementById("childWindowsList");
      const childUrl = document.getElementById("childUrl");

      // Initialize
      document.getElementById("parentId").textContent = PARENT_ID;

      function log(message, type = "info") {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
        messageLog.textContent += logEntry;
        messageLog.scrollTop = messageLog.scrollHeight;
        console.log(`Parent: ${message}`);
      }

      function updateStats() {
        document.getElementById(
          "childWindowCount"
        ).textContent = `${childWindows.size} windows open`;
        document.getElementById(
          "messageStats"
        ).textContent = `Sent: ${sentMessages}, Received: ${receivedMessages}`;
      }

      function updateChildWindowsList() {
        if (childWindows.size === 0) {
          childWindowsList.innerHTML =
            '<div style="color: #ccc; font-style: italic;">No child windows open</div>';
        } else {
          childWindowsList.innerHTML = Array.from(childWindows.entries())
            .map(
              ([id, info]) => `
                        <div class="window-item">
                            <div class="window-info">
                                <strong>${info.name}</strong><br>
                                <small>ID: ${id}</small><br>
                                <small>Status: ${
                                  info.closed ? "Closed" : "Open"
                                }</small>
                            </div>
                            <div class="window-controls">
                                <button class="btn-secondary" onclick="sendMessageToChild('${id}')">Message</button>
                                <button class="btn-danger" onclick="closeChildWindow('${id}')">Close</button>
                            </div>
                        </div>
                    `
            )
            .join("");
        }
        updateStats();
      }

      function openChildWindow() {
        const url = childUrl.value || "child.html";
        const width = parseInt(document.getElementById("popupWidth").value);
        const height = parseInt(document.getElementById("popupHeight").value);
        const left = parseInt(document.getElementById("popupLeft").value);
        const top = parseInt(document.getElementById("popupTop").value);

        const features = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`;

        try {
          const childWindow = window.open(url, "_blank", features);

          if (childWindow) {
            const childId =
              "child_" +
              Date.now() +
              "_" +
              Math.random().toString(36).substr(2, 9);

            childWindows.set(childId, {
              window: childWindow,
              name: `Child ${childWindows.size + 1}`,
              url: url,
              opened: Date.now(),
              closed: false,
            });

            log(`🪟 Opened child window: ${childId}`, "success");
            updateChildWindowsList();

            // Monitor if window gets closed
            const checkClosed = setInterval(() => {
              if (childWindow.closed) {
                clearInterval(checkClosed);
                const info = childWindows.get(childId);
                if (info) {
                  info.closed = true;
                  log(`🪟 Child window closed: ${childId}`, "info");
                  updateChildWindowsList();
                }
              }
            }, 1000);
          } else {
            log("❌ Failed to open child window (popup blocked?)", "error");
          }
        } catch (error) {
          log(`❌ Error opening child window: ${error.message}`, "error");
        }
      }

      function openMultipleWindows() {
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            // Offset each window position
            document.getElementById("popupLeft").value = 100 + i * 50;
            document.getElementById("popupTop").value = 100 + i * 50;
            openChildWindow();
          }, i * 200);
        }
      }

      function closeChildWindow(childId) {
        const childInfo = childWindows.get(childId);
        if (childInfo && !childInfo.window.closed) {
          childInfo.window.close();
          log(`🪟 Closed child window: ${childId}`, "info");
        }
      }

      function closeAllWindows() {
        for (const [childId, info] of childWindows.entries()) {
          if (!info.window.closed) {
            info.window.close();
          }
        }
        log("🪟 Closed all child windows", "info");
      }

      function sendMessageToChild(childId) {
        const message = prompt("Enter message for this child window:");
        if (message) {
          sendToSpecificChild(childId, {
            type: "direct_message",
            payload: { message: message },
            parentId: PARENT_ID,
            messageId: ++messageCounter,
            timestamp: Date.now(),
          });
        }
      }

      function sendMessageToAll() {
        const message = messageInput.value.trim();
        if (!message) {
          log("❌ Cannot send empty message", "error");
          return;
        }

        const messageData = {
          type: "broadcast_message",
          payload: { message: message },
          parentId: PARENT_ID,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        sendToAllChildren(messageData);
        log(`📤 Sent broadcast message: "${message}"`, "send");
        sentMessages++;
        updateStats();
      }

      function requestChildInfo() {
        const requestData = {
          type: "info_request",
          payload: {},
          parentId: PARENT_ID,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        sendToAllChildren(requestData);
        log("📋 Requested info from all children", "send");
        sentMessages++;
        updateStats();
      }

      function sendDataUpdate() {
        const updateData = {
          type: "data_update",
          payload: {
            config: {
              theme: "dark",
              language: "en",
              notifications: true,
            },
            user: {
              id: 12345,
              name: "John Doe",
              role: "admin",
            },
            timestamp: new Date().toISOString(),
          },
          parentId: PARENT_ID,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        sendToAllChildren(updateData);
        log("📊 Sent data update to all children", "send");
        sentMessages++;
        updateStats();
      }

      function pingAllChildren() {
        const pingData = {
          type: "ping",
          payload: { timestamp: Date.now() },
          parentId: PARENT_ID,
          messageId: ++messageCounter,
          timestamp: Date.now(),
        };

        sendToAllChildren(pingData);
        log("🏓 Pinged all children", "send");
        sentMessages++;
        updateStats();
      }

      function sendShutdownSignal() {
        if (
          confirm(
            "Are you sure you want to send shutdown signal to all child windows?"
          )
        ) {
          const shutdownData = {
            type: "shutdown",
            payload: { reason: "Parent initiated shutdown" },
            parentId: PARENT_ID,
            messageId: ++messageCounter,
            timestamp: Date.now(),
          };

          sendToAllChildren(shutdownData);
          log("🛑 Sent shutdown signal to all children", "send");
          sentMessages++;
          updateStats();
        }
      }

      function sendToAllChildren(data) {
        let successCount = 0;
        let failCount = 0;

        for (const [childId, info] of childWindows.entries()) {
          if (!info.window.closed) {
            try {
              info.window.postMessage(data, window.location.origin);
              successCount++;
            } catch (error) {
              log(`❌ Failed to send to ${childId}: ${error.message}`, "error");
              failCount++;
            }
          }
        }

        if (successCount > 0) {
          log(`✅ Message sent to ${successCount} children`, "success");
        }
        if (failCount > 0) {
          log(`❌ Failed to send to ${failCount} children`, "error");
        }
      }

      function sendToSpecificChild(childId, data) {
        const childInfo = childWindows.get(childId);
        if (childInfo && !childInfo.window.closed) {
          try {
            childInfo.window.postMessage(data, window.location.origin);
            log(`✅ Message sent to ${childId}`, "success");
            sentMessages++;
            updateStats();
          } catch (error) {
            log(`❌ Failed to send to ${childId}: ${error.message}`, "error");
          }
        } else {
          log(`❌ Child window ${childId} not available`, "error");
        }
      }

      // Listen for messages from child windows
      window.addEventListener("message", function (event) {
        // Security check: validate origin
        if (event.origin !== window.location.origin) {
          log(
            `❌ SECURITY: Rejected message from unauthorized origin: ${event.origin}`,
            "security"
          );
          return;
        }

        // Validate message structure
        if (!event.data || typeof event.data !== "object") {
          log(`❌ SECURITY: Invalid message format received`, "security");
          return;
        }

        const { type, payload, childId, messageId } = event.data;

        log(`📥 Received from child: ${type} (ID: ${messageId})`, "receive");
        receivedMessages++;
        updateStats();

        // Handle different message types
        switch (type) {
          case "child_ready":
            log(`✅ Child ready: ${childId}`, "success");
            log(`  URL: ${payload.url}`, "info");
            break;

          case "child_info":
            log(`📊 Child info from ${childId}:`, "info");
            log(`  URL: ${payload.url}`, "info");
            log(
              `  Viewport: ${payload.viewport.width}x${payload.viewport.height}`,
              "info"
            );
            log(
              `  User Agent: ${payload.userAgent.substring(0, 50)}...`,
              "info"
            );
            break;

          case "pong":
            const latency = Date.now() - payload.originalTimestamp;
            log(`🏓 Pong from ${childId} (latency: ${latency}ms)`, "success");
            break;

          case "child_message":
            log(`💬 Message from ${childId}: "${payload.message}"`, "message");
            break;

          case "child_error":
            log(`❌ Error from ${childId}: ${payload.error}`, "error");
            break;

          case "child_closing":
            log(`👋 Child ${childId} is closing`, "info");
            break;

          default:
            log(`⚠️ Unknown message type from child: ${type}`, "warning");
        }
      });

      function clearLog() {
        messageLog.textContent = "";
        log("📋 Log cleared", "info");
      }

      // Handle Enter key in message input
      messageInput.addEventListener("keypress", function (event) {
        if (event.key === "Enter") {
          sendMessageToAll();
        }
      });

      // Clean up when parent window is closing
      window.addEventListener("beforeunload", function () {
        // Send closing notification to all children
        const closingData = {
          type: "parent_closing",
          payload: { reason: "Parent window closing" },
          parentId: PARENT_ID,
          timestamp: Date.now(),
        };

        sendToAllChildren(closingData);

        // Close all child windows
        for (const [childId, info] of childWindows.entries()) {
          if (!info.window.closed) {
            info.window.close();
          }
        }
      });

      // Periodically check for closed windows
      setInterval(() => {
        let hasChanges = false;
        for (const [childId, info] of childWindows.entries()) {
          if (!info.closed && info.window.closed) {
            info.closed = true;
            hasChanges = true;
          }
        }
        if (hasChanges) {
          updateChildWindowsList();
        }
      }, 2000);

      // Initial log entry
      log("🚀 Parent window initialized and ready", "success");
      log(`Parent ID: ${PARENT_ID}`, "info");
      log('Click "Open Child Window" to start communication', "info");
    </script>
  </body>
</html>

// Import CDP client, video capture manager, and WebRTC manager
importScripts("cdp-client.js");
importScripts("video-capture.js");
importScripts("webrtc-manager.js");

// Background service worker for Video Stream Extension
class VideoStreamExtension {
  constructor() {
    this.activeStreams = new Map();
    this.signalingServer = null;
    this.cdpClient = new CDPClient();
    this.videoCaptureManager = new VideoCaptureManager();
    this.webrtcManager = new WebRTCManager();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      console.log("Video Stream Extension installed");
    });

    // Handle messages from popup and content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle tab updates
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === "complete") {
        this.onTabUpdated(tabId, tab);
      }
    });

    // Handle tab removal
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.stopStreamForTab(tabId);
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case "startCapture":
          console.log({ message });
          const result = await this.startVideoCapture(message.tabId);
          sendResponse({ success: true, data: result });
          break;

        case "stopCapture":
          await this.stopVideoCapture(message.tabId);
          sendResponse({ success: true });
          break;

        case "getActiveTab":
          const activeTab = await this.getActiveTab();
          sendResponse({ success: true, data: activeTab });
          break;

        case "connectSignaling":
          await this.connectToSignalingServer(message.serverUrl);
          sendResponse({ success: true });
          break;

        case "getCaptureStream":
          const stream = await this.getCaptureStream(message.tabId);
          sendResponse({ success: true, data: stream });
          break;

        case "attachCDP":
          const cdpResult = await this.cdpClient.attachToTab(message.tabId);
          sendResponse({ success: true, data: cdpResult });
          break;

        case "detachCDP":
          await this.cdpClient.detachFromTab(message.tabId);
          sendResponse({ success: true });
          break;

        case "getPageInfo":
          const pageInfo = await this.cdpClient.getPageInfo(message.tabId);
          sendResponse({ success: true, data: pageInfo });
          break;

        case "injectCaptureHelper":
          const helperResult = await this.cdpClient.injectCaptureHelper(
            message.tabId
          );
          sendResponse({ success: true, data: helperResult });
          break;

        case "enhanceVideoElements":
          const enhanceResult = await this.cdpClient.enhanceVideoElements(
            message.tabId
          );
          sendResponse({ success: true, data: enhanceResult });
          break;

        case "captureCanvas":
          const canvasResult = await this.cdpClient.captureCanvasStream(
            message.tabId,
            message.canvasIndex
          );
          sendResponse({ success: true, data: canvasResult });
          break;

        case "getCaptureStats":
          const stats = await this.videoCaptureManager.getCaptureStats(
            message.tabId
          );
          sendResponse({ success: true, data: stats });
          break;

        case "updateCaptureConstraints":
          await this.videoCaptureManager.updateCaptureConstraints(
            message.tabId,
            message.constraints
          );
          sendResponse({ success: true });
          break;

        case "getAllActiveCaptures":
          const captures = this.videoCaptureManager.getAllActiveCaptures();
          sendResponse({ success: true, data: captures });
          break;

        case "startStreaming":
          await this.startStreaming(message.tabId);
          sendResponse({ success: true });
          break;

        case "stopStreaming":
          await this.stopStreaming();
          sendResponse({ success: true });
          break;

        case "getStreamingStats":
          const streamingStats = this.webrtcManager.getConnectionStats();
          sendResponse({ success: true, data: streamingStats });
          break;

        case "getDetailedStreamingStats":
          const detailedStats = await this.webrtcManager.getDetailedStats(
            message.clientId
          );
          sendResponse({ success: true, data: detailedStats });
          break;

        default:
          sendResponse({ success: false, error: "Unknown action" });
      }
    } catch (error) {
      console.error("Background script error:", error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getActiveTab() {
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });
    return tab;
  }

  async startVideoCapture(tabId, options = {}) {
    try {
      // Use the video capture manager
      const captureInfo = await this.videoCaptureManager.startTabCapture(
        tabId,
        options
      );

      // Store stream info for compatibility
      this.activeStreams.set(tabId, {
        streamId: captureInfo.streamId,
        timestamp: captureInfo.startTime,
        captureInfo,
      });

      return {
        streamId: captureInfo.streamId,
        tabId,
        stream: captureInfo.stream,
      };
    } catch (error) {
      throw new Error(`Failed to start capture: ${error.message}`);
    }
  }

  async stopVideoCapture(tabId) {
    if (this.activeStreams.has(tabId)) {
      // Stop capture using video capture manager
      await this.videoCaptureManager.stopCapture(tabId);
      this.activeStreams.delete(tabId);

      // Detach debugger if attached
      try {
        await chrome.debugger.detach({ tabId });
      } catch (error) {
        // Ignore if not attached
      }
    }
  }

  async getCaptureStream(tabId) {
    const streamInfo = this.activeStreams.get(tabId);
    if (!streamInfo) {
      throw new Error("No active stream for this tab");
    }
    return streamInfo;
  }

  async connectToSignalingServer(serverUrl) {
    try {
      await this.webrtcManager.connectToSignalingServer(serverUrl);
      this.signalingServer = serverUrl;
      console.log("Connected to signaling server:", serverUrl);
    } catch (error) {
      throw new Error(
        `Failed to connect to signaling server: ${error.message}`
      );
    }
  }

  async startStreaming(tabId) {
    try {
      // Get the active capture stream
      const captureInfo = this.videoCaptureManager.getActiveCapture(tabId);
      if (!captureInfo) {
        throw new Error("No active capture for this tab. Start capture first.");
      }

      // Set the stream for WebRTC streaming
      await this.webrtcManager.setLocalStream(captureInfo.stream);

      console.log(`Started streaming for tab ${tabId}`);
    } catch (error) {
      throw new Error(`Failed to start streaming: ${error.message}`);
    }
  }

  async stopStreaming() {
    try {
      this.webrtcManager.disconnect();
      console.log("Stopped streaming");
    } catch (error) {
      throw new Error(`Failed to stop streaming: ${error.message}`);
    }
  }

  onTabUpdated(tabId, tab) {
    // Handle tab updates if needed
    console.log("Tab updated:", tabId, tab.url);
  }

  stopStreamForTab(tabId) {
    if (this.activeStreams.has(tabId)) {
      this.stopVideoCapture(tabId);
    }

    // Also detach CDP if attached
    if (this.cdpClient.isAttached(tabId)) {
      this.cdpClient.detachFromTab(tabId).catch(console.error);
    }
  }
}

// Initialize the extension
const videoStreamExtension = new VideoStreamExtension();

# PostMessage API Demonstrations with CDP Integration

This repository contains comprehensive examples demonstrating the browser's `window.postMessage` API across various scenarios, enhanced with Chrome DevTools Protocol (CDP) monitoring capabilities for advanced debugging.

## 🎯 Overview

The examples cover:

1. **Cross-origin iframe communication** - Parent page ↔ iframe from different origin
2. **Inter-tab communication** - Communication between browser tabs
3. **Parent-child window communication** - Parent tab ↔ popup window
4. **Complex multi-tab scenario** - Tab1 ↔ child window opened by Tab2
5. **CDP monitoring tools** - Enhanced debugging and monitoring

## 📁 Project Structure

```
postmessage-demos/
├── README.md                     # This file
├── cross-origin-iframe/          # Cross-origin iframe examples
│   ├── parent.html              # Main parent page
│   ├── iframe.html              # Iframe content
│   └── README.md                # Specific instructions
├── inter-tab/                   # Inter-tab communication
│   ├── tab1.html               # First tab
│   ├── tab2.html               # Second tab
│   └── README.md               # Specific instructions
├── parent-child-window/         # Parent-child window examples
│   ├── parent.html             # Parent window
│   ├── child.html              # Child popup
│   └── README.md               # Specific instructions
├── multi-tab-complex/           # Complex multi-tab scenario
│   ├── tab1.html               # Tab 1
│   ├── tab2.html               # Tab 2 (opens child)
│   ├── child.html              # Child window
│   └── README.md               # Specific instructions
├── cdp-monitoring/              # CDP monitoring tools
│   ├── monitor.js              # CDP monitoring script
│   ├── debug-ui.html           # Debug interface
│   └── README.md               # CDP setup instructions
└── servers/                     # Development server configs
    ├── server1.js              # Server for origin 1 (port 3001)
    ├── server2.js              # Server for origin 2 (port 3002)
    └── start-servers.js        # Script to start all servers
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- Chrome browser (for CDP features)
- Basic understanding of JavaScript and browser APIs

### Setup

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development servers:
   ```bash
   npm run start-servers
   ```
4. (Optional) Start CDP monitor for advanced debugging:
   ```bash
   npm run cdp-monitor
   ```
5. Open the examples in your browser

### Available Servers

- **Origin 1**: http://localhost:3001 (Primary origin)
- **Origin 2**: http://localhost:3002 (Secondary origin for cross-origin tests)
- **CDP Monitor**: http://localhost:3003 (Monitoring interface)

### Example URLs

- **Cross-Origin Iframe**: http://localhost:3001/cross-origin-iframe/parent.html
- **Inter-Tab Communication**: http://localhost:3001/inter-tab/tab1.html
- **Parent-Child Window**: http://localhost:3001/parent-child-window/parent.html
- **Complex Multi-Tab**: http://localhost:3001/multi-tab-complex/tab1.html

## 🔧 Testing Each Example

### 1. Cross-Origin Iframe Communication

```bash
# Start servers
npm run start-servers

# Open in browser
http://localhost:3001/cross-origin-iframe/parent.html
```

### 2. Inter-Tab Communication

```bash
# Open first tab
http://localhost:3001/inter-tab/tab1.html

# Open second tab
http://localhost:3001/inter-tab/tab2.html
```

### 3. Parent-Child Window Communication

```bash
# Open parent window
http://localhost:3001/parent-child-window/parent.html
# Child window will be opened automatically
```

### 4. Complex Multi-Tab Scenario

```bash
# Open Tab 1
http://localhost:3001/multi-tab-complex/tab1.html

# Open Tab 2
http://localhost:3001/multi-tab-complex/tab2.html
# Follow instructions to create child window
```

### 5. CDP Monitoring

```bash
# Start CDP monitor
node cdp-monitoring/monitor.js

# Open debug interface
http://localhost:3003/debug-ui.html
```

## 🛡️ Security Considerations

All examples include proper security measures:

- **Origin validation** - Checking `event.origin` before processing messages
- **Message validation** - Validating message structure and content
- **Error handling** - Comprehensive error handling and logging
- **CSP headers** - Content Security Policy implementation where applicable

## 🐛 Debugging Features

### Console Logging

Each example includes detailed console logging:

- Message sending events
- Message receiving events
- Origin validation results
- Error conditions

### CDP Integration

Enhanced debugging capabilities:

- Real-time message monitoring across all contexts
- Origin and data inspection
- Cross-origin communication debugging
- Performance metrics

## 📚 Learning Objectives

By working through these examples, you'll learn:

- How to implement secure cross-origin communication
- Best practices for message validation and error handling
- Advanced debugging techniques using CDP
- Real-world patterns for complex multi-window scenarios

## 🔗 Useful Resources

- [MDN: Window.postMessage()](https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [Web Security: Same-Origin Policy](https://developer.mozilla.org/en-US/docs/Web/Security/Same-origin_policy)

## 🤝 Contributing

Feel free to extend these examples or add new scenarios. Each example should be:

- Self-contained and well-documented
- Include proper error handling
- Demonstrate security best practices
- Provide clear testing instructions
